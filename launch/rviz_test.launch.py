import os
import launch
import launch_ros
from ament_index_python.packages import get_package_share_directory
from launch.actions import DeclareLaunchArgument, TimerAction
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    # 获取包路径
    pkg_gazebo = get_package_share_directory('pkg_gazebo')
    
    # 模型文件路径
    urdf_file = os.path.join(pkg_gazebo, 'urdf', 'urbubing.urdf.xacro')
    
    # 机器人描述参数
    robot_description = launch_ros.parameter_descriptions.ParameterValue(
        launch.substitutions.Command(['xacro ', urdf_file]),
        value_type=str
    )
    
    # 机器人状态发布器
    robot_state_publisher = launch_ros.actions.Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'robot_description': robot_description}]
    )
    
    # 关节状态发布器（带GUI）
    joint_state_publisher_gui = launch_ros.actions.Node(
        package='joint_state_publisher_gui',
        executable='joint_state_publisher_gui',
        name='joint_state_publisher_gui',
        output='screen'
    )
    
    # 麦克纳姆轮控制器（延迟启动）
    mecanum_controller = TimerAction(
        period=2.0,
        actions=[
            launch_ros.actions.Node(
                package='pkg_gazebo',
                executable='mecanum_controller.py',
                name='mecanum_controller',
                output='screen'
            )
        ]
    )
    
    # 启动RViz
    rviz_config_file = os.path.join(pkg_gazebo, 'config', 'rviz', 'robot_view.rviz')
    rviz = launch_ros.actions.Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        arguments=['-d', rviz_config_file] if os.path.exists(rviz_config_file) else [],
        output='screen'
    )
    
    return launch.LaunchDescription([
        robot_state_publisher,
        joint_state_publisher_gui,
        mecanum_controller,
        rviz
    ])
