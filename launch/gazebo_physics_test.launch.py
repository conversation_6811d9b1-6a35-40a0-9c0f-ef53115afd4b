import os
import launch
import launch_ros
from ament_index_python.packages import get_package_share_directory
from launch.actions import IncludeLaunchDescription, DeclareLaunchArgument
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    # 获取包路径
    pkg_gazebo = get_package_share_directory('pkg_gazebo')
    
    # Launch参数
    headless_arg = DeclareLaunchArgument(
        'headless',
        default_value='false',
        description='Run Gazebo in headless mode (no GUI)'
    )
    
    verbose_arg = DeclareLaunchArgument(
        'verbose',
        default_value='true',
        description='Enable verbose output'
    )
    
    world_arg = DeclareLaunchArgument(
        'world',
        default_value=os.path.join(pkg_gazebo, 'worlds', 'empty.world'),
        description='World file to load'
    )
    
    spawn_x_arg = DeclareLaunchArgument(
        'spawn_x',
        default_value='0.0',
        description='X position to spawn robot'
    )
    
    spawn_y_arg = DeclareLaunchArgument(
        'spawn_y', 
        default_value='0.0',
        description='Y position to spawn robot'
    )
    
    spawn_z_arg = DeclareLaunchArgument(
        'spawn_z',
        default_value='0.2',
        description='Z position to spawn robot (height above ground)'
    )
    
    # 模型文件路径
    urdf_file = os.path.join(pkg_gazebo, 'urdf', 'urbubing.urdf.xacro')
    
    # 机器人描述参数
    robot_description = launch_ros.parameter_descriptions.ParameterValue(
        launch.substitutions.Command(['xacro ', urdf_file]),
        value_type=str
    )
    
    # 启动Gazebo Harmonic
    gazebo = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(get_package_share_directory('ros_gz_sim'), 'launch', 'gz_sim.launch.py')
        ]),
        launch_arguments={
            'gz_args': [LaunchConfiguration('world'), ' -v 4'],
            'on_exit_shutdown': 'true'
        }.items()
    )
    
    # 机器人状态发布器
    robot_state_publisher = launch_ros.actions.Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'robot_description': robot_description}]
    )
    
    # 在Gazebo中生成机器人
    spawn_entity = launch_ros.actions.Node(
        package='ros_gz_sim',
        executable='create',
        arguments=[
            '-topic', 'robot_description',
            '-name', 'urbubing',
            '-x', LaunchConfiguration('spawn_x'),
            '-y', LaunchConfiguration('spawn_y'),
            '-z', LaunchConfiguration('spawn_z')
        ],
        output='screen'
    )
    
    # 关节状态发布器（用于监控关节状态）
    joint_state_publisher = launch_ros.actions.Node(
        package='joint_state_publisher',
        executable='joint_state_publisher',
        name='joint_state_publisher',
        output='screen'
    )
    
    return launch.LaunchDescription([
        headless_arg,
        verbose_arg,
        world_arg,
        spawn_x_arg,
        spawn_y_arg,
        spawn_z_arg,
        gazebo,
        robot_state_publisher,
        spawn_entity,
        joint_state_publisher
    ])
