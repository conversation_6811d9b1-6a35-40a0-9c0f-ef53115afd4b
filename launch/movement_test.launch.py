import os
import launch
import launch_ros
from ament_index_python.packages import get_package_share_directory
from launch.actions import IncludeLaunchDescription, DeclareLaunchArgument, TimerAction
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration


def generate_launch_description():
    # 获取包路径
    pkg_gazebo = get_package_share_directory('pkg_gazebo')
    
    # Launch参数
    headless_arg = DeclareLaunchArgument(
        'headless',
        default_value='false',
        description='Run Gazebo in headless mode (no GUI)'
    )
    
    spawn_x_arg = DeclareLaunchArgument(
        'spawn_x',
        default_value='0.0',
        description='X position to spawn robot'
    )
    
    spawn_y_arg = DeclareLaunchArgument(
        'spawn_y', 
        default_value='0.0',
        description='Y position to spawn robot'
    )
    
    spawn_z_arg = DeclareLaunchArgument(
        'spawn_z',
        default_value='0.2',
        description='Z position to spawn robot (height above ground)'
    )
    
    # 模型文件路径
    urdf_file = os.path.join(pkg_gazebo, 'urdf', 'urbubing.urdf.xacro')
    world_file = os.path.join(pkg_gazebo, 'worlds', 'movement_test.world')
    controllers_file = os.path.join(pkg_gazebo, 'config', 'controllers.yaml')
    
    # 机器人描述参数
    robot_description = launch_ros.parameter_descriptions.ParameterValue(
        launch.substitutions.Command(['xacro ', urdf_file]),
        value_type=str
    )
    
    # 启动Gazebo
    gazebo_args = [world_file, ' -v 4']
    if LaunchConfiguration('headless').perform(launch.LaunchContext()) == 'true':
        gazebo_args.append(' -s')  # server mode for headless
    
    gazebo = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([
            os.path.join(get_package_share_directory('ros_gz_sim'), 'launch', 'gz_sim.launch.py')
        ]),
        launch_arguments={
            'gz_args': ' '.join(gazebo_args),
            'on_exit_shutdown': 'true'
        }.items()
    )
    
    # 机器人状态发布器
    robot_state_publisher = launch_ros.actions.Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{'robot_description': robot_description}]
    )
    
    # 在Gazebo中生成机器人
    spawn_entity = launch_ros.actions.Node(
        package='ros_gz_sim',
        executable='create',
        arguments=[
            '-topic', 'robot_description',
            '-name', 'urbubing',
            '-x', LaunchConfiguration('spawn_x'),
            '-y', LaunchConfiguration('spawn_y'), 
            '-z', LaunchConfiguration('spawn_z')
        ],
        output='screen'
    )
    
    # 控制器管理器
    controller_manager = launch_ros.actions.Node(
        package='controller_manager',
        executable='ros2_control_node',
        parameters=[{'robot_description': robot_description}, controllers_file],
        output='screen'
    )
    
    # 延迟启动控制器spawner
    joint_state_broadcaster_spawner = TimerAction(
        period=3.0,
        actions=[
            launch_ros.actions.Node(
                package='controller_manager',
                executable='spawner',
                arguments=['joint_state_broadcaster'],
                output='screen'
            )
        ]
    )
    
    wheel_controller_spawner = TimerAction(
        period=4.0,
        actions=[
            launch_ros.actions.Node(
                package='controller_manager',
                executable='spawner',
                arguments=['wheel_controller'],
                output='screen'
            )
        ]
    )
    
    # 麦克纳姆轮控制器
    mecanum_controller = TimerAction(
        period=5.0,
        actions=[
            launch_ros.actions.Node(
                package='pkg_gazebo',
                executable='mecanum_controller.py',
                name='mecanum_controller',
                output='screen'
            )
        ]
    )
    
    # ROS-Gazebo桥接
    gz_ros_bridge = launch_ros.actions.Node(
        package='ros_gz_bridge',
        executable='parameter_bridge',
        arguments=[
            '/clock@rosgraph_msgs/msg/Clock[gz.msgs.Clock',
            '/tf@tf2_msgs/msg/TFMessage[gz.msgs.Pose_V',
            '/tf_static@tf2_msgs/msg/TFMessage[gz.msgs.Pose_V'
        ],
        output='screen'
    )
    
    return launch.LaunchDescription([
        headless_arg,
        spawn_x_arg,
        spawn_y_arg,
        spawn_z_arg,
        gazebo,
        robot_state_publisher,
        spawn_entity,
        controller_manager,
        joint_state_broadcaster_spawner,
        wheel_controller_spawner,
        mecanum_controller,
        gz_ros_bridge
    ])
