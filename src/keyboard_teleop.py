#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from std_msgs.msg import Float64MultiArray, Float64
import sys
import select
import termios
import tty
import math
import threading
import time

class AdvancedKeyboardTeleop(Node):
    def __init__(self):
        super().__init__('advanced_keyboard_teleop')

        # 发布话题
        self.cmd_vel_pub = self.create_publisher(Twist, '/cmd_vel', 10)
        self.wheel_cmd_pub = self.create_publisher(Float64MultiArray, '/wheel_controller/commands', 10)
        self.gimbal_cmd_pub = self.create_publisher(Float64MultiArray, '/gimbal_controller/commands', 10)
        self.shooter_cmd_pub = self.create_publisher(Float64, '/shooter_controller/command', 10)

        # 车辆运动参数
        self.linear_speed = 1.0    # 线速度 (m/s)
        self.angular_speed = 1.0   # 角速度 (rad/s)
        self.speed_step = 0.1      # 速度调节步长

        # 云台参数
        self.gimbal_yaw = 0.0      # 当前偏航角 (rad)
        self.gimbal_pitch = 0.0    # 当前俯仰角 (rad)
        self.gimbal_speed = 0.5    # 云台调节速度 (rad/s)
        self.gimbal_step = 0.1     # 云台调节步长 (rad)

        # 云台限制
        self.pitch_min = -0.7854   # -45度
        self.pitch_max = 0.5236    # 30度

        # 发射器参数
        self.shooter_speed = 0.0   # 发射器速度
        self.shooter_max_speed = 10.0  # 最大发射器速度

        # 当前状态
        self.current_linear_x = 0.0
        self.current_linear_y = 0.0
        self.current_angular_z = 0.0

        # 麦克纳姆轮参数
        self.wheel_radius = 0.05
        self.wheel_base_width = 0.35
        self.wheel_base_length = 0.38
        self.lx = self.wheel_base_length / 2.0
        self.ly = self.wheel_base_width / 2.0

        # 保存终端设置
        self.settings = termios.tcgetattr(sys.stdin)

        # 控制模式
        self.control_mode = 'vehicle'  # 'vehicle' 或 'gimbal'

        # 启动状态更新线程
        self.status_thread = threading.Thread(target=self.status_update_loop, daemon=True)
        self.status_thread.start()

        self.print_instructions()
        
    def print_instructions(self):
        """打印控制说明"""
        print("\n" + "="*80)
        print("🚗 高级麦克纳姆轮机器人键盘控制系统")
        print("="*80)
        print(f"当前控制模式: {'🚗 车辆控制' if self.control_mode == 'vehicle' else '🎯 云台控制'}")
        print("")
        print("模式切换:")
        print("   TAB : 切换控制模式 (车辆 ↔ 云台)")
        print("")
        print("🚗 车辆移动控制:")
        print("   w/s : 前进/后退")
        print("   a/d : 左移/右移")
        print("   q/e : 左转/右转")
        print("")
        print("组合移动 (斜向移动):")
        print("   r : 右前斜移    t : 左前斜移")
        print("   f : 右后斜移    g : 左后斜移")
        print("")
        print("🎯 云台/炮台控制:")
        print("   ↑/↓ : 俯仰调节 (上/下)")
        print("   ←/→ : 偏航调节 (左/右)")
        print("   z/x : 发射器启动/停止")
        print("   c/v : 发射器加速/减速")
        print("")
        print("⚙️ 速度控制:")
        print("   u/j : 增加/减少线速度")
        print("   i/k : 增加/减少角速度")
        print("   o/l : 增加/减少云台速度")
        print("   空格: 紧急停止所有运动")
        print("")
        print("其他:")
        print("   h : 显示帮助")
        print("   Ctrl+C : 退出")
        print("="*80)
        self.print_current_status()
        print("按任意键开始控制...")

    def print_current_status(self):
        """打印当前状态"""
        print(f"📊 当前设置:")
        print(f"   车辆速度: 线速度={self.linear_speed:.1f}m/s, 角速度={self.angular_speed:.1f}rad/s")
        print(f"   云台位置: 偏航={math.degrees(self.gimbal_yaw):.1f}°, 俯仰={math.degrees(self.gimbal_pitch):.1f}°")
        print(f"   云台速度: {self.gimbal_speed:.1f}rad/s")
        print(f"   发射器速度: {self.shooter_speed:.1f}rad/s")

    def status_update_loop(self):
        """状态更新循环"""
        while rclpy.ok():
            time.sleep(0.1)  # 10Hz更新频率
        
    def get_key(self):
        """获取键盘输入，支持特殊键"""
        try:
            tty.setraw(sys.stdin.fileno())
            # 使用非阻塞读取
            if select.select([sys.stdin], [], [], 0.1)[0]:
                key = sys.stdin.read(1)
                # 检查是否是特殊键（箭头键等）
                if key == '\x1b':  # ESC序列开始
                    if select.select([sys.stdin], [], [], 0.1)[0]:
                        key += sys.stdin.read(1)
                        if key == '\x1b[':
                            if select.select([sys.stdin], [], [], 0.1)[0]:
                                key += sys.stdin.read(1)
            else:
                key = ''
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.settings)
            return key
        except Exception as e:
            self.get_logger().error(f'键盘读取错误: {e}')
            return ''
        
    def publish_twist(self, linear_x=0.0, linear_y=0.0, angular_z=0.0):
        """发布Twist消息并转换为轮子速度"""
        # 发布cmd_vel
        twist = Twist()
        twist.linear.x = linear_x
        twist.linear.y = linear_y
        twist.angular.z = angular_z
        self.cmd_vel_pub.publish(twist)

        # 直接计算并发布轮子速度
        self.publish_wheel_commands(linear_x, linear_y, angular_z)

        # 更新当前速度
        self.current_linear_x = linear_x
        self.current_linear_y = linear_y
        self.current_angular_z = angular_z

    def publish_wheel_commands(self, vx, vy, wz):
        """计算并发布轮子速度命令"""
        # 麦克纳姆轮运动学逆解
        v_lf = (vx - vy - wz * (self.lx + self.ly))  # 左前轮
        v_rf = (vx + vy - wz * (self.lx + self.ly))  # 右前轮
        v_rb = (vx - vy + wz * (self.lx + self.ly))  # 右后轮
        v_lb = (vx + vy + wz * (self.lx + self.ly))  # 左后轮

        # 转换为轮子角速度
        omega_lf = v_lf / self.wheel_radius
        omega_rf = v_rf / self.wheel_radius
        omega_rb = v_rb / self.wheel_radius
        omega_lb = v_lb / self.wheel_radius

        # 发布轮子速度命令
        wheel_cmd = Float64MultiArray()
        wheel_cmd.data = [omega_lf, omega_rf, omega_rb, omega_lb]
        self.wheel_cmd_pub.publish(wheel_cmd)

    def publish_gimbal_commands(self):
        """发布云台位置命令"""
        gimbal_cmd = Float64MultiArray()
        gimbal_cmd.data = [self.gimbal_yaw, self.gimbal_pitch]
        self.gimbal_cmd_pub.publish(gimbal_cmd)

    def publish_shooter_command(self, speed):
        """发布发射器速度命令"""
        shooter_cmd = Float64()
        shooter_cmd.data = speed
        self.shooter_cmd_pub.publish(shooter_cmd)
        self.shooter_speed = speed

    def print_status(self):
        """打印当前状态"""
        mode_str = "🚗 车辆" if self.control_mode == 'vehicle' else "🎯 云台"
        print(f"\r[{mode_str}] 车辆: vx={self.current_linear_x:.2f}, vy={self.current_linear_y:.2f}, wz={self.current_angular_z:.2f} | "
              f"云台: yaw={math.degrees(self.gimbal_yaw):.1f}°, pitch={math.degrees(self.gimbal_pitch):.1f}° | "
              f"发射器: {self.shooter_speed:.1f}rad/s", end='', flush=True)
        
    def run(self):
        """主控制循环"""
        try:
            while rclpy.ok():
                key = self.get_key()

                if key == '\x03':  # Ctrl+C
                    break

                # 模式切换
                elif key == '\t':  # TAB键切换模式
                    self.control_mode = 'gimbal' if self.control_mode == 'vehicle' else 'vehicle'
                    print(f"\n切换到 {'🎯 云台控制' if self.control_mode == 'gimbal' else '🚗 车辆控制'} 模式")

                # 车辆控制模式
                elif self.control_mode == 'vehicle':
                    if key == 'w':  # 前进
                        self.publish_twist(linear_x=self.linear_speed)
                        print(f"\n🚗 前进 (速度: {self.linear_speed:.1f}m/s)")

                    elif key == 's':  # 后退
                        self.publish_twist(linear_x=-self.linear_speed)
                        print(f"\n🚗 后退 (速度: {self.linear_speed:.1f}m/s)")

                    elif key == 'a':  # 左移
                        self.publish_twist(linear_y=self.linear_speed)
                        print(f"\n🚗 左移 (速度: {self.linear_speed:.1f}m/s)")

                    elif key == 'd':  # 右移
                        self.publish_twist(linear_y=-self.linear_speed)
                        print(f"\n🚗 右移 (速度: {self.linear_speed:.1f}m/s)")

                    elif key == 'q':  # 左转
                        self.publish_twist(angular_z=self.angular_speed)
                        print(f"\n🚗 左转 (角速度: {self.angular_speed:.1f}rad/s)")

                    elif key == 'e':  # 右转
                        self.publish_twist(angular_z=-self.angular_speed)
                        print(f"\n🚗 右转 (角速度: {self.angular_speed:.1f}rad/s)")

                    # 斜向移动
                    elif key == 'r':  # 右前斜移
                        self.publish_twist(linear_x=self.linear_speed, linear_y=-self.linear_speed)
                        print(f"\n🚗 右前斜移")

                    elif key == 't':  # 左前斜移
                        self.publish_twist(linear_x=self.linear_speed, linear_y=self.linear_speed)
                        print(f"\n🚗 左前斜移")

                    elif key == 'f':  # 右后斜移
                        self.publish_twist(linear_x=-self.linear_speed, linear_y=-self.linear_speed)
                        print(f"\n🚗 右后斜移")

                    elif key == 'g':  # 左后斜移
                        self.publish_twist(linear_x=-self.linear_speed, linear_y=self.linear_speed)
                        print(f"\n🚗 左后斜移")

                # 云台控制模式
                elif self.control_mode == 'gimbal':
                    if key == '\x1b[A':  # 上箭头 - 俯仰向上
                        self.gimbal_pitch = min(self.pitch_max, self.gimbal_pitch + self.gimbal_step)
                        self.publish_gimbal_commands()
                        print(f"\n🎯 俯仰向上: {math.degrees(self.gimbal_pitch):.1f}°")

                    elif key == '\x1b[B':  # 下箭头 - 俯仰向下
                        self.gimbal_pitch = max(self.pitch_min, self.gimbal_pitch - self.gimbal_step)
                        self.publish_gimbal_commands()
                        print(f"\n🎯 俯仰向下: {math.degrees(self.gimbal_pitch):.1f}°")

                    elif key == '\x1b[D':  # 左箭头 - 偏航向左
                        self.gimbal_yaw += self.gimbal_step
                        self.publish_gimbal_commands()
                        print(f"\n🎯 偏航向左: {math.degrees(self.gimbal_yaw):.1f}°")

                    elif key == '\x1b[C':  # 右箭头 - 偏航向右
                        self.gimbal_yaw -= self.gimbal_step
                        self.publish_gimbal_commands()
                        print(f"\n🎯 偏航向右: {math.degrees(self.gimbal_yaw):.1f}°")

                    elif key == 'z':  # 启动发射器
                        self.publish_shooter_command(self.shooter_max_speed)
                        print(f"\n🔥 发射器启动: {self.shooter_speed:.1f}rad/s")

                    elif key == 'x':  # 停止发射器
                        self.publish_shooter_command(0.0)
                        print(f"\n⏹️ 发射器停止")

                    elif key == 'c':  # 发射器加速
                        new_speed = min(self.shooter_max_speed, self.shooter_speed + 1.0)
                        self.publish_shooter_command(new_speed)
                        print(f"\n⚡ 发射器加速: {self.shooter_speed:.1f}rad/s")

                    elif key == 'v':  # 发射器减速
                        new_speed = max(0.0, self.shooter_speed - 1.0)
                        self.publish_shooter_command(new_speed)
                        print(f"\n🔽 发射器减速: {self.shooter_speed:.1f}rad/s")

                # 通用控制
                # 速度调节
                if key == 'u':  # 增加线速度
                    self.linear_speed = min(3.0, self.linear_speed + self.speed_step)
                    print(f"\n⬆️ 线速度增加到: {self.linear_speed:.1f}m/s")

                elif key == 'j':  # 减少线速度
                    self.linear_speed = max(0.1, self.linear_speed - self.speed_step)
                    print(f"\n⬇️ 线速度减少到: {self.linear_speed:.1f}m/s")

                elif key == 'i':  # 增加角速度
                    self.angular_speed = min(3.0, self.angular_speed + self.speed_step)
                    print(f"\n⬆️ 角速度增加到: {self.angular_speed:.1f}rad/s")

                elif key == 'k':  # 减少角速度
                    self.angular_speed = max(0.1, self.angular_speed - self.speed_step)
                    print(f"\n⬇️ 角速度减少到: {self.angular_speed:.1f}rad/s")

                elif key == 'o':  # 增加云台速度
                    self.gimbal_speed = min(2.0, self.gimbal_speed + 0.1)
                    self.gimbal_step = self.gimbal_speed * 0.2
                    print(f"\n⬆️ 云台速度增加到: {self.gimbal_speed:.1f}rad/s")

                elif key == 'l':  # 减少云台速度
                    self.gimbal_speed = max(0.1, self.gimbal_speed - 0.1)
                    self.gimbal_step = self.gimbal_speed * 0.2
                    print(f"\n⬇️ 云台速度减少到: {self.gimbal_speed:.1f}rad/s")

                # 停止
                elif key == ' ':  # 空格键停止
                    self.publish_twist()
                    self.publish_shooter_command(0.0)
                    print(f"\n🛑 紧急停止所有运动!")

                # 帮助
                elif key == 'h':
                    self.print_instructions()

                else:
                    # 未知按键，在车辆模式下停止运动
                    if self.control_mode == 'vehicle' and key != '':
                        self.publish_twist()

                if key != '':
                    self.print_status()
                
        except Exception as e:
            self.get_logger().error(f'Error: {e}')
        finally:
            # 确保停止机器人和云台
            self.publish_twist()
            self.publish_shooter_command(0.0)
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.settings)

def main(args=None):
    rclpy.init(args=args)

    teleop = AdvancedKeyboardTeleop()

    try:
        teleop.run()
    except KeyboardInterrupt:
        pass
    finally:
        teleop.destroy_node()
        rclpy.shutdown()
        print("\n🎮 高级键盘控制已退出")

if __name__ == '__main__':
    main()
