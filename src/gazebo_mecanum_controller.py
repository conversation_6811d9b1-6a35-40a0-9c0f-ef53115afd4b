#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from std_msgs.msg import Float64
import math

class GazeboMecanumController(Node):
    def __init__(self):
        super().__init__('gazebo_mecanum_controller')
        
        # 订阅cmd_vel话题
        self.cmd_vel_sub = self.create_subscription(
            Twist,
            '/cmd_vel',
            self.cmd_vel_callback,
            10
        )
        
        # 发布到Gazebo关节控制话题
        self.wheel_lf_pub = self.create_publisher(Float64, '/model/urbubing/joint/wheel_lf_Joint/cmd_vel', 10)
        self.wheel_rf_pub = self.create_publisher(Float64, '/model/urbubing/joint/wheel_rf_Joint/cmd_vel', 10)
        self.wheel_rb_pub = self.create_publisher(Float64, '/model/urbubing/joint/wheel_rb_Joint/cmd_vel', 10)
        self.wheel_lb_pub = self.create_publisher(Float64, '/model/urbubing/joint/wheel_lb_Joint/cmd_vel', 10)
        
        # 机器人参数 (单位: 米)
        self.wheel_radius = 0.05  # 轮子半径
        self.wheel_base_width = 0.35  # 轮距 (左右轮子间距)
        self.wheel_base_length = 0.38  # 轴距 (前后轮子间距)
        
        # 计算机器人几何参数
        self.lx = self.wheel_base_length / 2.0  # 前轮到中心距离
        self.ly = self.wheel_base_width / 2.0   # 左轮到中心距离
        
        # 创建定时器定期发布零速度（保持连接）
        self.timer = self.create_timer(0.1, self.timer_callback)
        self.last_cmd_time = self.get_clock().now()
        self.timeout_duration = 0.5  # 0.5秒超时
        
        # 当前命令
        self.current_vx = 0.0
        self.current_vy = 0.0
        self.current_wz = 0.0
        
        self.get_logger().info('Gazebo Mecanum Controller initialized')
        self.get_logger().info(f'Wheel radius: {self.wheel_radius}m')
        self.get_logger().info(f'Wheel base: {self.wheel_base_width}m x {self.wheel_base_length}m')
        
    def cmd_vel_callback(self, msg):
        """
        将cmd_vel转换为四个麦克纳姆轮的速度命令
        """
        
        # 更新最后命令时间
        self.last_cmd_time = self.get_clock().now()
        
        # 提取线速度和角速度
        vx = msg.linear.x   # 前进/后退速度 (m/s)
        vy = msg.linear.y   # 左右平移速度 (m/s)
        wz = msg.angular.z  # 旋转角速度 (rad/s)
        
        # 保存当前命令
        self.current_vx = vx
        self.current_vy = vy
        self.current_wz = wz
        
        # 发布轮子速度
        self.publish_wheel_velocities(vx, vy, wz)
        
    def publish_wheel_velocities(self, vx, vy, wz):
        """计算并发布轮子速度"""
        
        # 计算每个轮子的线速度 (m/s)
        v_lf = (vx - vy - wz * (self.lx + self.ly))  # 左前轮
        v_rf = (vx + vy - wz * (self.lx + self.ly))  # 右前轮
        v_rb = (vx - vy + wz * (self.lx + self.ly))  # 右后轮
        v_lb = (vx + vy + wz * (self.lx + self.ly))  # 左后轮
        
        # 转换为轮子角速度 (rad/s)
        omega_lf = v_lf / self.wheel_radius
        omega_rf = v_rf / self.wheel_radius
        omega_rb = v_rb / self.wheel_radius
        omega_lb = v_lb / self.wheel_radius
        
        # 创建并发布速度命令
        lf_msg = Float64()
        lf_msg.data = omega_lf
        self.wheel_lf_pub.publish(lf_msg)
        
        rf_msg = Float64()
        rf_msg.data = omega_rf
        self.wheel_rf_pub.publish(rf_msg)
        
        rb_msg = Float64()
        rb_msg.data = omega_rb
        self.wheel_rb_pub.publish(rb_msg)
        
        lb_msg = Float64()
        lb_msg.data = omega_lb
        self.wheel_lb_pub.publish(lb_msg)
        
        # 调试信息
        if abs(vx) > 0.01 or abs(vy) > 0.01 or abs(wz) > 0.01:
            self.get_logger().info(
                f'Cmd: vx={vx:.2f}, vy={vy:.2f}, wz={wz:.2f} | '
                f'Wheels: lf={omega_lf:.2f}, rf={omega_rf:.2f}, '
                f'rb={omega_rb:.2f}, lb={omega_lb:.2f} rad/s'
            )
    
    def timer_callback(self):
        """定时器回调，检查命令超时"""
        current_time = self.get_clock().now()
        time_since_last_cmd = (current_time - self.last_cmd_time).nanoseconds / 1e9
        
        # 如果超时，发送零速度命令
        if time_since_last_cmd > self.timeout_duration:
            if abs(self.current_vx) > 0.01 or abs(self.current_vy) > 0.01 or abs(self.current_wz) > 0.01:
                self.current_vx = 0.0
                self.current_vy = 0.0
                self.current_wz = 0.0
                self.publish_wheel_velocities(0.0, 0.0, 0.0)
                self.get_logger().info('Command timeout - stopping robot')

def main(args=None):
    rclpy.init(args=args)
    
    controller = GazeboMecanumController()
    
    try:
        rclpy.spin(controller)
    except KeyboardInterrupt:
        pass
    finally:
        controller.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
