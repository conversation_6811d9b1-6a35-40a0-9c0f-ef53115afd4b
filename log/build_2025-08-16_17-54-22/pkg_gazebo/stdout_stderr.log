-- The C compiler identification is GNU 13.3.0
-- The CXX compiler identification is GNU 13.3.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: 
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Configured 'flake8' exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done (0.6s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo
-- Install configuration: ""
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch/display.launch.py
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//urdf
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.urdf
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link3.STL
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/dials_Link.STL
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/base_link.STL
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rb_Link.STL
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lf_Link.STL
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link2.STL
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link1.STL
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rf_Link.STL
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_Link.STL
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lb_Link.STL
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/yaw_Link.STL
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/joint_names_urbubing.yaml
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/rviz
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/rviz/display_model.py
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/package_run_dependencies/pkg_gazebo
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/parent_prefix_path/pkg_gazebo
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/path.sh
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/path.dsv
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.bash
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.sh
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.zsh
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.dsv
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/package.dsv
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/packages/pkg_gazebo
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig.cmake
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig-version.cmake
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/package.xml
