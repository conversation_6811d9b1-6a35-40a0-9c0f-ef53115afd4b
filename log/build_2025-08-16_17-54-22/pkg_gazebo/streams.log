[0.010s] Invoking command in '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ros2_ws/src/pkg_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo
[0.060s] -- The C compiler identification is GNU 13.3.0
[0.107s] -- The CXX compiler identification is GNU 13.3.0
[0.122s] -- Detecting C compiler ABI info
[0.205s] -- Detecting C compiler ABI info - done
[0.212s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.212s] -- Detecting C compile features
[0.212s] -- Detecting C compile features - done
[0.221s] -- Detecting CXX compiler ABI info
[0.309s] -- Detecting CXX compiler ABI info - done
[0.316s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.316s] -- Detecting CXX compile features
[0.316s] -- Detecting CXX compile features - done
[0.322s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.463s] -- Found Python3: /usr/bin/python3 (found version "3.12.3") found components: Interpreter 
[0.561s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[0.644s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.644s] -- Configured cppcheck include dirs: 
[0.644s] -- Configured cppcheck exclude dirs and/or files: 
[0.645s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.645s] -- Configured 'flake8' exclude dirs and/or files: 
[0.647s] -- Added test 'lint_cmake' to check CMake code style
[0.648s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.652s] -- Added test 'uncrustify' to check C / C++ code style
[0.652s] -- Configured uncrustify additional arguments: 
[0.653s] -- Added test 'xmllint' to check XML markup files
[0.655s] -- Configuring done (0.6s)
[0.657s] -- Generating done (0.0s)
[0.658s] -- Build files have been written to: /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo
[0.662s] Invoked command in '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake /home/<USER>/ros2_ws/src/pkg_gazebo -DCMAKE_INSTALL_PREFIX=/home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo
[0.663s] Invoking command in '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo -- -j32 -l32
[0.691s] Invoked command in '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo -- -j32 -l32
[0.697s] Invoking command in '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo
[0.702s] -- Install configuration: ""
[0.702s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch
[0.702s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch/display.launch.py
[0.702s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//urdf
[0.702s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.urdf
[0.702s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes
[0.702s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link3.STL
[0.713s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/dials_Link.STL
[0.714s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/base_link.STL
[0.725s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rb_Link.STL
[0.727s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lf_Link.STL
[0.729s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link2.STL
[0.729s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link1.STL
[0.730s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rf_Link.STL
[0.732s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_Link.STL
[0.733s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lb_Link.STL
[0.735s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/yaw_Link.STL
[0.738s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config
[0.738s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/joint_names_urbubing.yaml
[0.738s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/rviz
[0.738s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/rviz/display_model.py
[0.738s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/package_run_dependencies/pkg_gazebo
[0.738s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/parent_prefix_path/pkg_gazebo
[0.738s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.sh
[0.738s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.dsv
[0.738s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/path.sh
[0.738s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/path.dsv
[0.739s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.bash
[0.739s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.sh
[0.739s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.zsh
[0.739s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.dsv
[0.739s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/package.dsv
[0.739s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/packages/pkg_gazebo
[0.739s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig.cmake
[0.739s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig-version.cmake
[0.739s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/package.xml
[0.741s] Invoked command in '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo
