-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: 
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Configured 'flake8' exclude dirs and/or files: 
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done (0.3s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo
-- Install configuration: ""
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch/display.launch.py
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//urdf
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.urdf
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link3.STL
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/dials_Link.STL
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/base_link.STL
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rb_Link.STL
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lf_Link.STL
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link2.STL
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link1.STL
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rf_Link.STL
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_Link.STL
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lb_Link.STL
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/yaw_Link.STL
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/joint_names_urbubing.yaml
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/rviz
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/rviz/display_model.py
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/package_run_dependencies/pkg_gazebo
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/parent_prefix_path/pkg_gazebo
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/path.sh
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/path.dsv
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.bash
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.sh
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.zsh
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.dsv
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/package.dsv
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/packages/pkg_gazebo
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig.cmake
-- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig-version.cmake
-- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/package.xml
