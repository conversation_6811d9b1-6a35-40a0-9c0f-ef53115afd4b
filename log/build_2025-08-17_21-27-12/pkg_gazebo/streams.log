[0.010s] Invoking command in '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo -- -j32 -l32
[0.039s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.237s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[0.306s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[0.307s] -- Configured cppcheck include dirs: 
[0.307s] -- Configured cppcheck exclude dirs and/or files: 
[0.308s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.308s] -- Configured 'flake8' exclude dirs and/or files: 
[0.310s] -- Added test 'lint_cmake' to check CMake code style
[0.311s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.320s] -- Added test 'uncrustify' to check C / C++ code style
[0.320s] -- Configured uncrustify additional arguments: 
[0.321s] -- Added test 'xmllint' to check XML markup files
[0.322s] -- Configuring done (0.3s)
[0.325s] -- Generating done (0.0s)
[0.326s] -- Build files have been written to: /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo
[0.346s] Invoked command in '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo -- -j32 -l32
[0.353s] Invoking command in '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo
[0.360s] -- Install configuration: ""
[0.360s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch
[0.360s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//launch/display.launch.py
[0.360s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//urdf
[0.360s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.urdf
[0.360s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes
[0.360s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link3.STL
[0.360s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/dials_Link.STL
[0.360s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/base_link.STL
[0.360s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rb_Link.STL
[0.360s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lf_Link.STL
[0.360s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link2.STL
[0.360s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link1.STL
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rf_Link.STL
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_Link.STL
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lb_Link.STL
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//meshes/yaw_Link.STL
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config
[0.361s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/joint_names_urbubing.yaml
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/rviz
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo//config/rviz/display_model.py
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/package_run_dependencies/pkg_gazebo
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/parent_prefix_path/pkg_gazebo
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.sh
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.dsv
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/path.sh
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/environment/path.dsv
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.bash
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.sh
[0.361s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.zsh
[0.362s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/local_setup.dsv
[0.362s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/package.dsv
[0.362s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/ament_index/resource_index/packages/pkg_gazebo
[0.362s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig.cmake
[0.362s] -- Up-to-date: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig-version.cmake
[0.362s] -- Installing: /home/<USER>/ros2_ws/src/pkg_gazebo/install/pkg_gazebo/share/pkg_gazebo/package.xml
[0.363s] Invoked command in '/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo
