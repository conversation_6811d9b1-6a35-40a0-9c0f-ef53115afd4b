#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from sensor_msgs.msg import JointState
import time
import math

class MotionTester(Node):
    def __init__(self):
        super().__init__('motion_tester')
        
        # 发布cmd_vel话题
        self.cmd_vel_pub = self.create_publisher(Twist, '/cmd_vel', 10)
        
        # 订阅关节状态
        self.joint_state_sub = self.create_subscription(
            JointState,
            '/joint_states',
            self.joint_state_callback,
            10
        )
        
        # 存储关节状态
        self.joint_states = {}
        self.last_wheel_positions = {}
        
        self.get_logger().info('🧪 运动测试器启动')
        self.get_logger().info('这个工具会发送测试命令并显示轮子的运动状态')
        
        # 创建定时器进行自动测试
        self.test_timer = self.create_timer(3.0, self.run_motion_test)
        self.test_count = 0
        
    def joint_state_callback(self, msg):
        """更新关节状态并检测运动"""
        wheel_joints = ['wheel_lf_Joint', 'wheel_rf_Joint', 'wheel_rb_Joint', 'wheel_lb_Joint']
        
        for i, name in enumerate(msg.name):
            if name in wheel_joints:
                current_pos = msg.position[i] if i < len(msg.position) else 0.0
                current_vel = msg.velocity[i] if i < len(msg.velocity) else 0.0
                
                # 检测位置变化
                if name in self.last_wheel_positions:
                    pos_change = abs(current_pos - self.last_wheel_positions[name])
                    if pos_change > 0.001:  # 如果位置有明显变化
                        self.get_logger().info(f'🔄 {name} 正在转动! 位置变化: {pos_change:.4f} rad')
                
                self.last_wheel_positions[name] = current_pos
                
                # 存储当前状态
                self.joint_states[name] = {
                    'position': current_pos,
                    'velocity': current_vel
                }
    
    def send_test_command(self, linear_x=0.0, linear_y=0.0, angular_z=0.0, duration=2.0, description=""):
        """发送测试命令"""
        self.get_logger().info(f'📤 发送命令: {description}')
        self.get_logger().info(f'   vx={linear_x:.1f}, vy={linear_y:.1f}, wz={angular_z:.1f}')
        
        twist = Twist()
        twist.linear.x = linear_x
        twist.linear.y = linear_y
        twist.angular.z = angular_z
        
        # 发送命令持续指定时间
        start_time = time.time()
        while time.time() - start_time < duration:
            self.cmd_vel_pub.publish(twist)
            time.sleep(0.1)
        
        # 停止
        stop_twist = Twist()
        self.cmd_vel_pub.publish(stop_twist)
        self.get_logger().info('⏹️  命令结束，机器人停止')
        
    def check_wheel_activity(self):
        """检查轮子活动状态"""
        wheel_joints = ['wheel_lf_Joint', 'wheel_rf_Joint', 'wheel_rb_Joint', 'wheel_lb_Joint']
        active_wheels = 0
        
        for wheel in wheel_joints:
            if wheel in self.joint_states:
                vel = abs(self.joint_states[wheel]['velocity'])
                if vel > 0.01:  # 速度阈值
                    active_wheels += 1
                    self.get_logger().info(f'✅ {wheel}: 速度 = {vel:.3f} rad/s')
                else:
                    self.get_logger().info(f'⭕ {wheel}: 静止')
        
        return active_wheels
    
    def run_motion_test(self):
        """运行运动测试序列"""
        test_commands = [
            (1.0, 0.0, 0.0, "前进测试"),
            (0.0, 1.0, 0.0, "左移测试（麦克纳姆轮特色）"),
            (0.0, 0.0, 1.0, "左转测试"),
            (1.0, 1.0, 0.0, "左前斜移测试"),
        ]
        
        if self.test_count < len(test_commands):
            vx, vy, wz, desc = test_commands[self.test_count]
            
            self.get_logger().info(f'\n🚀 测试 {self.test_count + 1}/{len(test_commands)}: {desc}')
            self.send_test_command(vx, vy, wz, 2.0, desc)
            
            # 检查轮子状态
            time.sleep(0.5)  # 等待状态更新
            active_wheels = self.check_wheel_activity()
            
            if active_wheels > 0:
                self.get_logger().info(f'✅ 测试成功! {active_wheels} 个轮子在运动')
            else:
                self.get_logger().warn('❌ 测试失败! 没有检测到轮子运动')
            
            self.test_count += 1
            
        else:
            self.get_logger().info('\n🏁 所有测试完成!')
            self.get_logger().info('如果您看到轮子运动信息，说明系统工作正常')
            self.get_logger().info('RViz中看不到运动是正常的，因为它只显示静态模型')
            self.test_timer.cancel()

def main(args=None):
    rclpy.init(args=args)
    
    tester = MotionTester()
    
    try:
        rclpy.spin(tester)
    except KeyboardInterrupt:
        pass
    finally:
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
