#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
import time

class DebugRobotSpawn(Node):
    def __init__(self):
        super().__init__('debug_robot_spawn')
        
        # 等待一段时间让系统启动
        time.sleep(2)
        
        self.check_robot_description()
        self.check_topics()
        
    def check_robot_description(self):
        """检查机器人描述话题"""
        self.get_logger().info("🔍 检查机器人描述话题...")
        
        # 检查话题是否存在
        topic_names = self.get_topic_names_and_types()
        robot_desc_found = False
        
        for topic_name, topic_types in topic_names:
            if 'robot_description' in topic_name:
                self.get_logger().info(f"✅ 找到话题: {topic_name} - 类型: {topic_types}")
                robot_desc_found = True
        
        if not robot_desc_found:
            self.get_logger().error("❌ 未找到robot_description话题")
        
    def check_topics(self):
        """检查所有相关话题"""
        self.get_logger().info("🔍 检查所有话题...")
        
        topic_names = self.get_topic_names_and_types()
        relevant_topics = []
        
        for topic_name, topic_types in topic_names:
            if any(keyword in topic_name.lower() for keyword in 
                   ['robot', 'joint', 'tf', 'cmd_vel', 'gazebo', 'model']):
                relevant_topics.append((topic_name, topic_types))
        
        if relevant_topics:
            self.get_logger().info("📋 相关话题列表:")
            for topic_name, topic_types in relevant_topics:
                self.get_logger().info(f"  - {topic_name}: {topic_types}")
        else:
            self.get_logger().warn("⚠️  未找到相关话题")

def main(args=None):
    rclpy.init(args=args)
    
    debug_node = DebugRobotSpawn()
    
    try:
        rclpy.spin_once(debug_node, timeout_sec=1.0)
    except KeyboardInterrupt:
        pass
    finally:
        debug_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
