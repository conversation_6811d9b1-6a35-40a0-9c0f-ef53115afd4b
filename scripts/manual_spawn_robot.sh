#!/bin/bash

# 手动生成机器人脚本
# 解决机器人在Gazebo中不显示的问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 确保环境已加载
source /home/<USER>/ros2_ws/install/setup.bash

echo "🤖 手动机器人生成工具"
echo "===================="

# 步骤1：检查robot_description话题
print_info "检查robot_description话题..."
if ros2 topic echo /robot_description --once --timeout 3 > /dev/null 2>&1; then
    print_success "robot_description话题有数据"
else
    print_error "robot_description话题无数据，请先启动robot_state_publisher"
    exit 1
fi

# 步骤2：获取URDF内容并保存到临时文件
print_info "获取URDF内容..."
TEMP_URDF="/tmp/urbubing_$(date +%s).urdf"
ros2 topic echo /robot_description --once --timeout 5 | grep -A 10000 "data:" | sed 's/data: //' | sed "s/'//" | sed 's/---//' > "$TEMP_URDF"

if [ -s "$TEMP_URDF" ]; then
    print_success "URDF内容已保存到: $TEMP_URDF"
    URDF_SIZE=$(wc -c < "$TEMP_URDF")
    print_info "URDF文件大小: $URDF_SIZE 字节"
else
    print_error "无法获取URDF内容"
    exit 1
fi

# 步骤3：尝试多种生成方法
print_info "尝试生成机器人..."

# 方法1：使用话题
print_info "方法1: 使用robot_description话题"
if ros2 run ros_gz_sim create -topic robot_description -name urbubing_v1 -x 0 -y 0 -z 1.0; then
    print_success "方法1成功"
    sleep 2
    if gz model -m urbubing_v1 -p > /dev/null 2>&1; then
        print_success "机器人urbubing_v1已在Gazebo中"
    fi
fi

# 方法2：使用文件
print_info "方法2: 使用URDF文件"
if ros2 run ros_gz_sim create -file "$TEMP_URDF" -name urbubing_v2 -x 2 -y 0 -z 1.0; then
    print_success "方法2成功"
    sleep 2
    if gz model -m urbubing_v2 -p > /dev/null 2>&1; then
        print_success "机器人urbubing_v2已在Gazebo中"
    fi
fi

# 方法3：使用不同的话题名称
print_info "方法3: 使用/robot_description话题"
if ros2 run ros_gz_sim create -topic /robot_description -name urbubing_v3 -x -2 -y 0 -z 1.0; then
    print_success "方法3成功"
    sleep 2
    if gz model -m urbubing_v3 -p > /dev/null 2>&1; then
        print_success "机器人urbubing_v3已在Gazebo中"
    fi
fi

# 方法4：使用更高的位置
print_info "方法4: 使用更高位置"
if ros2 run ros_gz_sim create -topic robot_description -name urbubing_v4 -x 0 -y 2 -z 3.0; then
    print_success "方法4成功"
    sleep 2
    if gz model -m urbubing_v4 -p > /dev/null 2>&1; then
        print_success "机器人urbubing_v4已在Gazebo中"
    fi
fi

# 步骤4：检查结果
print_info "检查生成结果..."
echo ""
echo "📋 当前Gazebo中的所有模型："
gz model --list

echo ""
echo "🎯 查找机器人的方法："
echo "1. 在Gazebo GUI中按 'R' 键重置相机"
echo "2. 使用鼠标滚轮大幅缩小视野"
echo "3. 在Entity Tree面板中查找 urbubing_v1, urbubing_v2, urbubing_v3, urbubing_v4"
echo "4. 右键点击机器人名称，选择 'Move to' 或 'Focus'"

echo ""
echo "🔧 如果仍然看不到机器人："
echo "1. 机器人可能太小，需要大幅缩小视野"
echo "2. 机器人可能在地下，检查Z坐标"
echo "3. 机器人可能透明，检查材质设置"

# 清理临时文件
rm -f "$TEMP_URDF"

print_success "脚本执行完成！"
