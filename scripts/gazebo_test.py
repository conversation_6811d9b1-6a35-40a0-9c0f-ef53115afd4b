#!/usr/bin/env python3

import subprocess
import time
import sys
import os

def check_gazebo_installation():
    """检查Gazebo安装状态"""
    print("🔍 检查Gazebo安装状态...")
    
    try:
        result = subprocess.run(['gz', 'sim', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Gazebo Garden 已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ Gazebo Garden 未正确安装")
            return False
    except Exception as e:
        print(f"❌ 检查Gazebo时出错: {e}")
        return False

def check_ros_packages():
    """检查必需的ROS包"""
    print("\n🔍 检查ROS2包...")
    
    required_packages = [
        'ros_gz_sim',
        'ros_gz_bridge', 
        'robot_state_publisher',
        'joint_state_publisher'
    ]
    
    all_good = True
    for package in required_packages:
        try:
            result = subprocess.run(['ros2', 'pkg', 'list'], 
                                  capture_output=True, text=True)
            if package in result.stdout:
                print(f"✅ {package} 已安装")
            else:
                print(f"❌ {package} 未安装")
                all_good = False
        except Exception as e:
            print(f"❌ 检查包 {package} 时出错: {e}")
            all_good = False
    
    return all_good

def test_world_file():
    """测试世界文件"""
    print("\n🔍 检查世界文件...")
    
    world_file = "/home/<USER>/ros2_ws/src/pkg_gazebo/worlds/movement_test.world"
    if os.path.exists(world_file):
        print(f"✅ 世界文件存在: {world_file}")
        return True
    else:
        print(f"❌ 世界文件不存在: {world_file}")
        return False

def test_urdf_file():
    """测试URDF文件"""
    print("\n🔍 检查URDF文件...")
    
    urdf_file = "/home/<USER>/ros2_ws/src/pkg_gazebo/urdf/urbubing.urdf.xacro"
    if os.path.exists(urdf_file):
        print(f"✅ URDF文件存在: {urdf_file}")
        
        # 测试xacro处理
        try:
            result = subprocess.run(['xacro', urdf_file], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ URDF文件语法正确")
                return True
            else:
                print(f"❌ URDF文件语法错误: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 处理URDF文件时出错: {e}")
            return False
    else:
        print(f"❌ URDF文件不存在: {urdf_file}")
        return False

def main():
    print("🚀 Gazebo可视化测试诊断")
    print("=" * 50)
    
    # 检查各个组件
    checks = [
        check_gazebo_installation(),
        check_ros_packages(),
        test_world_file(),
        test_urdf_file()
    ]
    
    print("\n" + "=" * 50)
    if all(checks):
        print("🎉 所有检查通过！系统准备就绪。")
        print("\n📋 建议的启动命令:")
        print("1. 启动仿真环境:")
        print("   ros2 launch pkg_gazebo gazebo_visual_test.launch.py")
        print("\n2. 在新终端启动键盘控制:")
        print("   python3 src/pkg_gazebo/src/keyboard_teleop.py")
    else:
        print("❌ 发现问题，请解决后重试。")
        print("\n🔧 可能的解决方案:")
        print("1. 安装缺失的包:")
        print("   sudo apt update")
        print("   sudo apt install ros-jazzy-ros-gz ros-jazzy-ros-gz-bridge")
        print("2. 重新构建项目:")
        print("   colcon build --packages-select pkg_gazebo")
        print("3. 加载环境:")
        print("   source install/setup.bash")

if __name__ == "__main__":
    main()
