#!/bin/bash

# 机器人可见性问题修复脚本
# 用于解决Gazebo Harmonic中机器人模型不可见的问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查机器人是否在Gazebo中
check_robot_in_gazebo() {
    print_info "检查机器人是否已加载到Gazebo中..."
    
    if command -v gz &> /dev/null; then
        MODELS=$(gz model --list 2>/dev/null || echo "")
        if echo "$MODELS" | grep -q "urbubing"; then
            print_success "机器人模型 'urbubing' 已在Gazebo中找到"
            return 0
        else
            print_warning "机器人模型 'urbubing' 未在Gazebo中找到"
            print_info "当前Gazebo中的模型："
            echo "$MODELS"
            return 1
        fi
    else
        print_error "Gazebo命令行工具不可用"
        return 1
    fi
}

# 检查ROS话题
check_ros_topics() {
    print_info "检查ROS话题..."
    
    if command -v ros2 &> /dev/null; then
        TOPICS=$(ros2 topic list 2>/dev/null || echo "")
        
        if echo "$TOPICS" | grep -q "/robot_description"; then
            print_success "找到 /robot_description 话题"
        else
            print_warning "未找到 /robot_description 话题"
        fi
        
        if echo "$TOPICS" | grep -q "/model/urbubing"; then
            print_success "找到机器人相关话题"
        else
            print_warning "未找到机器人相关话题"
        fi
        
        print_info "机器人相关话题："
        echo "$TOPICS" | grep -E "(robot|urbubing)" || echo "无"
    else
        print_error "ROS2命令不可用"
        return 1
    fi
}

# 手动生成机器人
spawn_robot_manually() {
    print_info "尝试手动生成机器人..."
    
    # 确保环境已加载
    if [ -f "/home/<USER>/ros2_ws/install/setup.bash" ]; then
        source /home/<USER>/ros2_ws/install/setup.bash
        print_success "已加载ROS2环境"
    else
        print_error "找不到ROS2环境设置文件"
        return 1
    fi
    
    # 检查robot_description话题是否有数据
    print_info "检查robot_description话题..."
    if ros2 topic echo /robot_description --once --timeout 5 > /dev/null 2>&1; then
        print_success "robot_description话题有数据"
        
        # 尝试手动生成机器人
        print_info "手动生成机器人到位置 (0, 0, 0.5)..."
        if ros2 run ros_gz_sim create -topic /robot_description -name urbubing -x 0 -y 0 -z 0.5; then
            print_success "机器人手动生成成功！"
            return 0
        else
            print_error "机器人手动生成失败"
            return 1
        fi
    else
        print_error "robot_description话题无数据或超时"
        return 1
    fi
}

# 提供Gazebo GUI操作指导
provide_gui_guidance() {
    echo ""
    echo "🎮 Gazebo GUI 操作指导："
    echo "=================================="
    echo "1. 查找机器人："
    echo "   - 在右侧面板找到 'Entity tree'"
    echo "   - 展开树形结构查找 'urbubing'"
    echo "   - 如果找到，右键点击选择 'Move to'"
    echo ""
    echo "2. 相机控制："
    echo "   - 鼠标左键拖拽：旋转视角"
    echo "   - 鼠标滚轮：缩放"
    echo "   - 鼠标中键拖拽：平移"
    echo "   - 按 'R' 键：重置相机"
    echo ""
    echo "3. 如果机器人太小："
    echo "   - 大幅缩小视野（滚轮向前）"
    echo "   - 寻找地面中心附近的小物体"
    echo ""
    echo "4. 检查可视化设置："
    echo "   - 右侧 'Visualization capabilities' 面板"
    echo "   - 确保所有选项都已启用"
    echo ""
}

# 生成诊断报告
generate_diagnostic_report() {
    print_info "生成诊断报告..."
    
    REPORT_FILE="/tmp/gazebo_robot_diagnostic_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "Gazebo机器人可见性诊断报告"
        echo "生成时间: $(date)"
        echo "=================================="
        echo ""
        
        echo "1. Gazebo版本信息："
        gz sim --versions 2>/dev/null || echo "无法获取版本信息"
        echo ""
        
        echo "2. Gazebo中的模型列表："
        gz model --list 2>/dev/null || echo "无法获取模型列表"
        echo ""
        
        echo "3. ROS2话题列表："
        ros2 topic list 2>/dev/null || echo "无法获取话题列表"
        echo ""
        
        echo "4. 机器人描述话题状态："
        if ros2 topic info /robot_description 2>/dev/null; then
            echo "robot_description话题存在"
        else
            echo "robot_description话题不存在"
        fi
        echo ""
        
        echo "5. 系统环境："
        echo "ROS_DISTRO: ${ROS_DISTRO:-未设置}"
        echo "ROS_DOMAIN_ID: ${ROS_DOMAIN_ID:-未设置}"
        echo ""
        
    } > "$REPORT_FILE"
    
    print_success "诊断报告已保存到: $REPORT_FILE"
    echo "您可以查看此文件了解详细信息"
}

# 主函数
main() {
    echo "🔧 Gazebo机器人可见性问题修复工具"
    echo "=================================="
    echo ""
    
    # 步骤1：检查机器人是否在Gazebo中
    if check_robot_in_gazebo; then
        print_info "机器人已在Gazebo中，问题可能是相机位置或缩放"
        provide_gui_guidance
    else
        print_warning "机器人未在Gazebo中找到，尝试修复..."
        
        # 步骤2：检查ROS话题
        check_ros_topics
        
        # 步骤3：尝试手动生成
        if spawn_robot_manually; then
            print_success "修复完成！机器人应该现在可见了"
        else
            print_error "自动修复失败，请查看GUI操作指导"
            provide_gui_guidance
        fi
    fi
    
    # 步骤4：生成诊断报告
    generate_diagnostic_report
    
    echo ""
    echo "🎯 快速解决方案："
    echo "1. 在Gazebo GUI中按 'R' 键重置相机"
    echo "2. 使用鼠标滚轮大幅缩小视野"
    echo "3. 在Entity Tree中查找并双击 'urbubing'"
    echo "4. 如果仍然看不到，运行以下命令手动生成："
    echo "   ros2 run ros_gz_sim create -topic /robot_description -name urbubing -x 0 -y 0 -z 1.0"
}

# 运行主函数
main "$@"
