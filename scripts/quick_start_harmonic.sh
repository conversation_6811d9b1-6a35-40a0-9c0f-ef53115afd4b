#!/bin/bash

# Gazebo Harmonic 快速启动脚本
# 作者: AI Assistant
# 版本: 1.0
# 描述: 用于快速启动不同类型的 Gazebo Harmonic 仿真

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    # 检查 ROS2
    if ! command -v ros2 &> /dev/null; then
        print_error "ROS2 未安装或未正确配置"
        exit 1
    fi
    
    # 检查 Gazebo
    if ! command -v gz &> /dev/null; then
        print_error "Gazebo 未安装"
        exit 1
    fi
    
    # 检查 Gazebo 版本
    GZ_VERSION=$(gz sim --versions 2>/dev/null || echo "unknown")
    if [[ "$GZ_VERSION" == "unknown" ]]; then
        print_error "无法获取 Gazebo 版本"
        exit 1
    fi
    
    print_success "Gazebo Harmonic 版本: $GZ_VERSION"
}

# 构建项目
build_project() {
    print_info "构建项目..."
    cd /home/<USER>/ros2_ws
    
    if colcon build --packages-select pkg_gazebo; then
        print_success "项目构建成功"
    else
        print_error "项目构建失败"
        exit 1
    fi
    
    source install/setup.bash
}

# 显示菜单
show_menu() {
    echo ""
    echo "=========================================="
    echo "    Gazebo Harmonic 仿真启动菜单"
    echo "=========================================="
    echo "1. 无头仿真 (适合测试)"
    echo "2. 可视化仿真 (推荐)"
    echo "3. 可视化仿真 (修复版) - 解决机器人不显示问题"
    echo "4. 物理测试仿真"
    echo "5. 运动测试仿真"
    echo "6. 仅启动键盘控制"
    echo "7. 系统诊断"
    echo "0. 退出"
    echo "=========================================="
}

# 启动无头仿真
start_headless() {
    print_info "启动无头仿真..."
    ros2 launch pkg_gazebo simple_gazebo_test.launch.py
}

# 启动可视化仿真
start_visual() {
    print_info "启动可视化仿真..."
    print_warning "这将打开 Gazebo GUI，请确保有足够的系统资源"
    ros2 launch pkg_gazebo gazebo_visual_test.launch.py
}

# 启动修复版可视化仿真
start_visual_fixed() {
    print_info "启动修复版可视化仿真..."
    print_warning "这将打开 Gazebo GUI，使用预处理的URDF文件"
    ros2 launch pkg_gazebo gazebo_visual_fixed.launch.py
}

# 启动物理测试
start_physics() {
    print_info "启动物理测试仿真..."
    ros2 launch pkg_gazebo gazebo_physics_test.launch.py
}

# 启动运动测试
start_movement() {
    print_info "启动运动测试仿真..."
    ros2 launch pkg_gazebo movement_test.launch.py
}

# 启动键盘控制
start_teleop() {
    print_info "启动键盘遥控..."
    print_warning "请确保仿真已经在另一个终端中运行"
    echo ""
    echo "控制说明:"
    echo "  w/s: 前进/后退    a/d: 左移/右移    q/e: 左转/右转"
    echo "  r: 右前斜移      t: 左前斜移      f: 右后斜移      g: 左后斜移"
    echo "  u/j: 增加/减少线速度    i/k: 增加/减少角速度"
    echo "  空格: 紧急停止    Ctrl+C: 退出"
    echo ""
    ros2 run pkg_gazebo keyboard_teleop.py
}

# 系统诊断
system_diagnosis() {
    print_info "执行系统诊断..."
    
    echo ""
    echo "=== ROS2 环境 ==="
    echo "ROS_DISTRO: ${ROS_DISTRO:-未设置}"
    echo "ROS_DOMAIN_ID: ${ROS_DOMAIN_ID:-未设置}"
    
    echo ""
    echo "=== Gazebo 信息 ==="
    echo "Gazebo 版本: $(gz sim --versions 2>/dev/null || echo '未知')"
    echo "Gazebo 路径: $(which gz 2>/dev/null || echo '未找到')"
    
    echo ""
    echo "=== ROS2 包检查 ==="
    if ros2 pkg list | grep -q "pkg_gazebo"; then
        print_success "pkg_gazebo 包已安装"
    else
        print_error "pkg_gazebo 包未找到"
    fi
    
    echo ""
    echo "=== Gazebo 相关包 ==="
    ros2 pkg list | grep -E "(ros_gz|gz_ros2)" | head -10
    
    echo ""
    echo "=== 当前运行的节点 ==="
    ros2 node list 2>/dev/null || echo "无活动节点"
    
    echo ""
    echo "=== 活动话题 ==="
    ros2 topic list 2>/dev/null | head -10 || echo "无活动话题"
}

# 主函数
main() {
    echo "欢迎使用 Gazebo Harmonic 仿真系统！"
    
    # 检查依赖
    check_dependencies
    
    # 构建项目
    build_project
    
    while true; do
        show_menu
        read -p "请选择操作 (0-7): " choice

        case $choice in
            1)
                start_headless
                ;;
            2)
                start_visual
                ;;
            3)
                start_visual_fixed
                ;;
            4)
                start_physics
                ;;
            5)
                start_movement
                ;;
            6)
                start_teleop
                ;;
            7)
                system_diagnosis
                read -p "按回车键继续..."
                ;;
            0)
                print_info "退出程序"
                exit 0
                ;;
            *)
                print_error "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按回车键返回主菜单..."
    done
}

# 运行主函数
main "$@"
