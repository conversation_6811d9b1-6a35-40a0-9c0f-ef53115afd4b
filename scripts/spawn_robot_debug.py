#!/usr/bin/env python3

"""
机器人生成调试脚本
用于诊断和解决机器人在Gazebo中不显示的问题
"""

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
import subprocess
import time
import sys

class RobotSpawnDebugger(Node):
    def __init__(self):
        super().__init__('robot_spawn_debugger')
        
        # 创建robot_description订阅者
        self.robot_desc_sub = self.create_subscription(
            String,
            '/robot_description',
            self.robot_description_callback,
            10
        )
        
        self.robot_description_received = False
        self.robot_description_content = ""
        
        self.get_logger().info("🔍 机器人生成调试器已启动")
        
    def robot_description_callback(self, msg):
        """接收robot_description消息"""
        self.robot_description_received = True
        self.robot_description_content = msg.data
        self.get_logger().info("✅ 接收到robot_description数据")
        self.get_logger().info(f"📏 URDF内容长度: {len(msg.data)} 字符")
        
        # 检查URDF内容的关键部分
        if "urbubing" in msg.data:
            self.get_logger().info("✅ URDF包含机器人名称 'urbubing'")
        else:
            self.get_logger().warn("⚠️ URDF中未找到机器人名称 'urbubing'")
            
        if "<robot" in msg.data:
            self.get_logger().info("✅ URDF格式正确（包含<robot>标签）")
        else:
            self.get_logger().error("❌ URDF格式错误（缺少<robot>标签）")

def check_gazebo_status():
    """检查Gazebo状态"""
    print("🔍 检查Gazebo状态...")
    
    try:
        # 检查Gazebo是否运行
        result = subprocess.run(['gz', 'model', '--list'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            models = result.stdout.strip().split('\n')
            print(f"✅ Gazebo正在运行，当前模型数量: {len(models)}")
            print("📋 当前模型列表:")
            for model in models:
                if model.strip():
                    print(f"   - {model}")
            
            if 'urbubing' in models:
                print("✅ 机器人 'urbubing' 已在Gazebo中")
                return True
            else:
                print("❌ 机器人 'urbubing' 不在Gazebo中")
                return False
        else:
            print("❌ 无法连接到Gazebo")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Gazebo命令超时")
        return False
    except Exception as e:
        print(f"❌ 检查Gazebo时出错: {e}")
        return False

def spawn_robot_manually():
    """手动生成机器人"""
    print("\n🚀 尝试手动生成机器人...")
    
    spawn_commands = [
        # 方法1：使用robot_description话题
        ['ros2', 'run', 'ros_gz_sim', 'create', 
         '-topic', 'robot_description', '-name', 'urbubing', 
         '-x', '0', '-y', '0', '-z', '0.5'],
        
        # 方法2：使用/robot_description话题
        ['ros2', 'run', 'ros_gz_sim', 'create', 
         '-topic', '/robot_description', '-name', 'urbubing', 
         '-x', '0', '-y', '0', '-z', '1.0'],
         
        # 方法3：使用更高的位置
        ['ros2', 'run', 'ros_gz_sim', 'create', 
         '-topic', 'robot_description', '-name', 'urbubing', 
         '-x', '0', '-y', '0', '-z', '2.0'],
    ]
    
    for i, cmd in enumerate(spawn_commands, 1):
        print(f"\n📝 尝试方法 {i}: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print(f"✅ 方法 {i} 成功！")
                print("输出:", result.stdout)
                return True
            else:
                print(f"❌ 方法 {i} 失败")
                print("错误:", result.stderr)
        except subprocess.TimeoutExpired:
            print(f"❌ 方法 {i} 超时")
        except Exception as e:
            print(f"❌ 方法 {i} 出错: {e}")
    
    return False

def check_urdf_file():
    """检查URDF文件"""
    print("\n🔍 检查URDF文件...")
    
    urdf_path = "/home/<USER>/ros2_ws/install/pkg_gazebo/share/pkg_gazebo/urdf/urbubing.urdf.xacro"
    
    try:
        # 使用xacro处理文件
        result = subprocess.run(['xacro', urdf_path], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ URDF文件语法正确")
            urdf_content = result.stdout
            
            # 检查关键元素
            if "<robot" in urdf_content:
                print("✅ 包含<robot>标签")
            if "urbubing" in urdf_content:
                print("✅ 包含机器人名称")
            if "<link" in urdf_content:
                print("✅ 包含链接定义")
            if "<joint" in urdf_content:
                print("✅ 包含关节定义")
                
            return True
        else:
            print("❌ URDF文件语法错误:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 检查URDF文件时出错: {e}")
        return False

def main():
    print("🤖 机器人生成调试工具")
    print("=" * 50)
    
    # 步骤1：检查URDF文件
    urdf_ok = check_urdf_file()
    
    # 步骤2：检查Gazebo状态
    robot_in_gazebo = check_gazebo_status()
    
    if robot_in_gazebo:
        print("\n🎉 机器人已在Gazebo中！")
        print("💡 如果您看不到机器人，请尝试：")
        print("   1. 在Gazebo GUI中按 'R' 键重置相机")
        print("   2. 使用鼠标滚轮大幅缩小视野")
        print("   3. 在Entity Tree中查找并双击 'urbubing'")
        return
    
    if not urdf_ok:
        print("\n❌ URDF文件有问题，请先修复URDF文件")
        return
    
    # 步骤3：初始化ROS2并检查robot_description话题
    print("\n🔍 检查ROS2话题...")
    rclpy.init()
    
    debugger = RobotSpawnDebugger()
    
    # 等待接收robot_description
    print("⏳ 等待robot_description话题...")
    start_time = time.time()
    while not debugger.robot_description_received and (time.time() - start_time) < 10:
        rclpy.spin_once(debugger, timeout_sec=0.1)
    
    if not debugger.robot_description_received:
        print("❌ 未接收到robot_description话题数据")
        print("💡 请确保robot_state_publisher正在运行")
        rclpy.shutdown()
        return
    
    # 步骤4：尝试手动生成机器人
    success = spawn_robot_manually()
    
    if success:
        print("\n🎉 机器人生成成功！")
        # 再次检查
        time.sleep(2)
        check_gazebo_status()
    else:
        print("\n❌ 所有生成方法都失败了")
        print("\n🔧 建议的解决方案：")
        print("1. 重启Gazebo仿真")
        print("2. 检查Gazebo日志中的错误信息")
        print("3. 确保所有ROS2包都正确安装")
        print("4. 尝试使用简化的URDF文件")
    
    rclpy.shutdown()

if __name__ == '__main__':
    main()
