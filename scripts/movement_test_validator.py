#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from sensor_msgs.msg import JointState
import time
import math

class MovementTestValidator(Node):
    def __init__(self):
        super().__init__('movement_test_validator')
        
        # 发布cmd_vel话题
        self.cmd_vel_pub = self.create_publisher(Twist, '/cmd_vel', 10)
        
        # 订阅关节状态
        self.joint_state_sub = self.create_subscription(
            JointState,
            '/joint_states',
            self.joint_state_callback,
            10
        )
        
        # 存储关节状态
        self.joint_states = {}
        self.test_results = []
        
        self.get_logger().info('Movement Test Validator Started')
        
    def joint_state_callback(self, msg):
        """更新关节状态"""
        for i, name in enumerate(msg.name):
            if 'wheel' in name:
                self.joint_states[name] = {
                    'position': msg.position[i] if i < len(msg.position) else 0.0,
                    'velocity': msg.velocity[i] if i < len(msg.velocity) else 0.0
                }
    
    def send_cmd_vel(self, linear_x=0.0, linear_y=0.0, angular_z=0.0, duration=2.0):
        """发送速度命令并持续指定时间"""
        twist = Twist()
        twist.linear.x = linear_x
        twist.linear.y = linear_y
        twist.angular.z = angular_z
        
        start_time = time.time()
        while time.time() - start_time < duration:
            self.cmd_vel_pub.publish(twist)
            time.sleep(0.1)
        
        # 停止
        twist = Twist()
        self.cmd_vel_pub.publish(twist)
        time.sleep(0.5)
    
    def check_wheel_movement(self, expected_direction):
        """检查轮子是否按预期方向转动"""
        wheel_names = ['wheel_lf_Joint', 'wheel_rf_Joint', 'wheel_rb_Joint', 'wheel_lb_Joint']
        moving_wheels = 0
        
        for wheel in wheel_names:
            if wheel in self.joint_states:
                velocity = abs(self.joint_states[wheel]['velocity'])
                if velocity > 0.1:  # 阈值：0.1 rad/s
                    moving_wheels += 1
        
        return moving_wheels >= 2  # 至少2个轮子在转动
    
    def test_forward_movement(self):
        """测试前进运动"""
        self.get_logger().info('🧪 测试前进运动...')
        
        # 记录初始位置
        initial_positions = dict(self.joint_states)
        
        # 发送前进命令
        self.send_cmd_vel(linear_x=1.0, duration=3.0)
        
        # 检查轮子是否转动
        result = self.check_wheel_movement('forward')
        
        if result:
            self.get_logger().info('✅ 前进运动测试通过')
            self.test_results.append(('前进运动', True))
        else:
            self.get_logger().warn('❌ 前进运动测试失败')
            self.test_results.append(('前进运动', False))
        
        return result
    
    def test_lateral_movement(self):
        """测试侧向运动（麦克纳姆轮特性）"""
        self.get_logger().info('🧪 测试左侧移动...')
        
        # 发送左移命令
        self.send_cmd_vel(linear_y=1.0, duration=3.0)
        
        # 检查轮子是否转动
        result = self.check_wheel_movement('lateral')
        
        if result:
            self.get_logger().info('✅ 侧向运动测试通过')
            self.test_results.append(('侧向运动', True))
        else:
            self.get_logger().warn('❌ 侧向运动测试失败')
            self.test_results.append(('侧向运动', False))
        
        return result
    
    def test_rotational_movement(self):
        """测试旋转运动"""
        self.get_logger().info('🧪 测试旋转运动...')
        
        # 发送旋转命令
        self.send_cmd_vel(angular_z=1.0, duration=3.0)
        
        # 检查轮子是否转动
        result = self.check_wheel_movement('rotation')
        
        if result:
            self.get_logger().info('✅ 旋转运动测试通过')
            self.test_results.append(('旋转运动', True))
        else:
            self.get_logger().warn('❌ 旋转运动测试失败')
            self.test_results.append(('旋转运动', False))
        
        return result
    
    def test_diagonal_movement(self):
        """测试斜向运动"""
        self.get_logger().info('🧪 测试斜向运动...')
        
        # 发送斜向命令（右前）
        self.send_cmd_vel(linear_x=1.0, linear_y=-1.0, duration=3.0)
        
        # 检查轮子是否转动
        result = self.check_wheel_movement('diagonal')
        
        if result:
            self.get_logger().info('✅ 斜向运动测试通过')
            self.test_results.append(('斜向运动', True))
        else:
            self.get_logger().warn('❌ 斜向运动测试失败')
            self.test_results.append(('斜向运动', False))
        
        return result
    
    def test_stop_command(self):
        """测试停止命令"""
        self.get_logger().info('🧪 测试停止命令...')
        
        # 先发送运动命令
        self.send_cmd_vel(linear_x=1.0, duration=1.0)
        
        # 发送停止命令
        self.send_cmd_vel(duration=2.0)
        
        # 检查轮子是否停止
        stopped_wheels = 0
        wheel_names = ['wheel_lf_Joint', 'wheel_rf_Joint', 'wheel_rb_Joint', 'wheel_lb_Joint']
        
        for wheel in wheel_names:
            if wheel in self.joint_states:
                velocity = abs(self.joint_states[wheel]['velocity'])
                if velocity < 0.1:  # 阈值：0.1 rad/s
                    stopped_wheels += 1
        
        result = stopped_wheels >= 3  # 至少3个轮子停止
        
        if result:
            self.get_logger().info('✅ 停止命令测试通过')
            self.test_results.append(('停止命令', True))
        else:
            self.get_logger().warn('❌ 停止命令测试失败')
            self.test_results.append(('停止命令', False))
        
        return result
    
    def run_all_tests(self):
        """运行所有测试"""
        self.get_logger().info('🚀 开始运行移动测试套件...')
        self.get_logger().info('等待关节状态数据...')
        
        # 等待关节状态数据
        timeout = 10.0
        start_time = time.time()
        while len(self.joint_states) < 4 and (time.time() - start_time) < timeout:
            rclpy.spin_once(self, timeout_sec=0.1)
        
        if len(self.joint_states) < 4:
            self.get_logger().error('❌ 无法获取足够的关节状态数据，测试中止')
            return
        
        self.get_logger().info(f'✅ 检测到 {len(self.joint_states)} 个轮子关节')
        
        # 运行测试序列
        tests = [
            self.test_forward_movement,
            self.test_lateral_movement,
            self.test_rotational_movement,
            self.test_diagonal_movement,
            self.test_stop_command
        ]
        
        for i, test in enumerate(tests, 1):
            self.get_logger().info(f'\n--- 测试 {i}/{len(tests)} ---')
            test()
            time.sleep(1.0)  # 测试间隔
        
        # 输出测试结果
        self.print_test_summary()
    
    def print_test_summary(self):
        """打印测试总结"""
        self.get_logger().info('\n' + '='*50)
        self.get_logger().info('🏁 移动测试结果总结')
        self.get_logger().info('='*50)
        
        passed_tests = 0
        total_tests = len(self.test_results)
        
        for test_name, result in self.test_results:
            status = '✅ 通过' if result else '❌ 失败'
            self.get_logger().info(f'{test_name}: {status}')
            if result:
                passed_tests += 1
        
        self.get_logger().info('-'*50)
        self.get_logger().info(f'总计: {passed_tests}/{total_tests} 测试通过')
        
        if passed_tests == total_tests:
            self.get_logger().info('🎉 所有测试通过！机器人移动功能正常')
        else:
            self.get_logger().warn(f'⚠️  {total_tests - passed_tests} 个测试失败，请检查系统配置')
        
        self.get_logger().info('='*50)

def main(args=None):
    rclpy.init(args=args)
    
    validator = MovementTestValidator()
    
    try:
        validator.run_all_tests()
        # 保持节点运行一段时间以完成所有测试
        time.sleep(2.0)
    except KeyboardInterrupt:
        pass
    finally:
        validator.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
