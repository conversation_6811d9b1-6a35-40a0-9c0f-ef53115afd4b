#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
import subprocess
import time
import os

class ManualRobotSpawn(Node):
    def __init__(self):
        super().__init__('manual_robot_spawn')
        
        # 等待Gazebo启动
        self.get_logger().info("等待Gazebo启动...")
        time.sleep(5)
        
        # 手动创建机器人
        self.spawn_simple_robot()
        
    def spawn_simple_robot(self):
        """手动在Gazebo中创建一个简单的机器人"""
        self.get_logger().info("🚀 手动创建机器人...")
        
        # 创建一个简单的SDF机器人
        sdf_content = '''<?xml version="1.0"?>
<sdf version="1.6">
  <model name="manual_robot">
    <pose>0 0 0.5 0 0 0</pose>
    <static>false</static>
    
    <link name="base_link">
      <pose>0 0 0 0 0 0</pose>
      
      <visual name="base_visual">
        <geometry>
          <box>
            <size>0.4 0.35 0.15</size>
          </box>
        </geometry>
        <material>
          <ambient>0.7 0.2 0.2 1</ambient>
          <diffuse>0.7 0.2 0.2 1</diffuse>
        </material>
      </visual>
      
      <collision name="base_collision">
        <geometry>
          <box>
            <size>0.4 0.35 0.15</size>
          </box>
        </geometry>
      </collision>
      
      <inertial>
        <mass>8.868</mass>
        <inertia>
          <ixx>0.1</ixx>
          <iyy>0.1</iyy>
          <izz>0.1</izz>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyz>0</iyz>
        </inertia>
      </inertial>
    </link>
    
    <!-- 前左轮 -->
    <link name="wheel_fl">
      <pose>0.19 0.175 -0.075 1.5708 0 0</pose>
      
      <visual name="wheel_fl_visual">
        <geometry>
          <cylinder>
            <radius>0.05</radius>
            <length>0.03</length>
          </cylinder>
        </geometry>
        <material>
          <ambient>0.2 0.2 0.2 1</ambient>
          <diffuse>0.2 0.2 0.2 1</diffuse>
        </material>
      </visual>
      
      <collision name="wheel_fl_collision">
        <geometry>
          <cylinder>
            <radius>0.05</radius>
            <length>0.03</length>
          </cylinder>
        </geometry>
      </collision>
      
      <inertial>
        <mass>0.255</mass>
        <inertia>
          <ixx>0.001</ixx>
          <iyy>0.001</iyy>
          <izz>0.001</izz>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyz>0</iyz>
        </inertia>
      </inertial>
    </link>
    
    <joint name="wheel_fl_joint" type="revolute">
      <parent>base_link</parent>
      <child>wheel_fl</child>
      <axis>
        <xyz>0 1 0</xyz>
      </axis>
      <limit>
        <lower>-1e16</lower>
        <upper>1e16</upper>
      </limit>
    </joint>
    
  </model>
</sdf>'''
        
        # 保存SDF文件
        sdf_file = '/tmp/manual_robot.sdf'
        with open(sdf_file, 'w') as f:
            f.write(sdf_content)
        
        # 使用gz命令创建机器人
        try:
            cmd = [
                'gz', 'service', '-s', '/world/movement_test/create',
                '--reqtype', 'gz.msgs.EntityFactory',
                '--reptype', 'gz.msgs.Boolean',
                '--timeout', '5000',
                '--req', f'sdf_filename: "{sdf_file}" name: "manual_robot"'
            ]
            
            self.get_logger().info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.get_logger().info("✅ 机器人创建成功!")
                self.get_logger().info(f"输出: {result.stdout}")
            else:
                self.get_logger().error(f"❌ 机器人创建失败: {result.stderr}")
                
        except Exception as e:
            self.get_logger().error(f"❌ 创建机器人时出错: {e}")
        
        # 列出所有模型
        self.list_models()
    
    def list_models(self):
        """列出Gazebo中的所有模型"""
        try:
            result = subprocess.run(['gz', 'model', '--list'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                self.get_logger().info(f"📋 Gazebo中的模型列表:\n{result.stdout}")
            else:
                self.get_logger().error(f"❌ 获取模型列表失败: {result.stderr}")
        except Exception as e:
            self.get_logger().error(f"❌ 获取模型列表时出错: {e}")

def main(args=None):
    rclpy.init(args=args)
    
    spawn_node = ManualRobotSpawn()
    
    try:
        rclpy.spin_once(spawn_node, timeout_sec=1.0)
    except KeyboardInterrupt:
        pass
    finally:
        spawn_node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
