#!/usr/bin/env python3

import subprocess
import sys
import os
import time

class SystemDiagnostics:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.info = []
        
    def log_error(self, msg):
        self.errors.append(msg)
        print(f"❌ 错误: {msg}")
        
    def log_warning(self, msg):
        self.warnings.append(msg)
        print(f"⚠️  警告: {msg}")
        
    def log_info(self, msg):
        self.info.append(msg)
        print(f"ℹ️  信息: {msg}")
        
    def log_success(self, msg):
        print(f"✅ 成功: {msg}")
        
    def run_command(self, cmd, description=""):
        """运行命令并返回结果"""
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return True, result.stdout.strip()
            else:
                return False, result.stderr.strip()
        except subprocess.TimeoutExpired:
            return False, f"命令超时: {cmd}"
        except Exception as e:
            return False, f"执行错误: {e}"
    
    def check_ros2_installation(self):
        """检查ROS2安装"""
        print("\n🔍 检查ROS2安装...")
        
        # 检查ROS2环境变量
        if 'ROS_DISTRO' not in os.environ:
            self.log_error("ROS_DISTRO环境变量未设置")
            return False
        
        distro = os.environ['ROS_DISTRO']
        self.log_success(f"ROS2发行版: {distro}")
        
        # 检查ros2命令
        success, output = self.run_command("which ros2")
        if success:
            self.log_success(f"ros2命令路径: {output}")
        else:
            self.log_error("ros2命令未找到")
            return False
            
        return True
    
    def check_dependencies(self):
        """检查依赖包"""
        print("\n🔍 检查依赖包...")
        
        required_packages = [
            'robot_state_publisher',
            'joint_state_publisher', 
            'xacro',
            'ros_gz_sim',
            'ros_gz_bridge'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            success, output = self.run_command(f"ros2 pkg list | grep {package}")
            if success and package in output:
                self.log_success(f"包已安装: {package}")
            else:
                missing_packages.append(package)
                self.log_error(f"包未安装: {package}")
        
        if missing_packages:
            self.log_info("安装缺失包的命令:")
            print(f"sudo apt install ros-{os.environ.get('ROS_DISTRO', 'jazzy')}-{' ros-{}-'.join(missing_packages).replace('_', '-')}")
            
        return len(missing_packages) == 0
    
    def check_workspace(self):
        """检查工作空间"""
        print("\n🔍 检查工作空间...")
        
        # 检查当前目录
        current_dir = os.getcwd()
        self.log_info(f"当前目录: {current_dir}")
        
        # 检查是否在ROS2工作空间中
        if 'ros2_ws' in current_dir:
            self.log_success("在ROS2工作空间中")
        else:
            self.log_warning("可能不在ROS2工作空间中")
        
        # 检查pkg_gazebo包
        pkg_path = None
        possible_paths = [
            'src/pkg_gazebo',
            '../pkg_gazebo',
            './pkg_gazebo'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                pkg_path = path
                break
        
        if pkg_path:
            self.log_success(f"找到pkg_gazebo包: {pkg_path}")
            
            # 检查关键文件
            key_files = [
                'urdf/urbubing.urdf.xacro',
                'src/mecanum_controller.py',
                'src/keyboard_teleop.py',
                'launch/basic_test.launch.py'
            ]
            
            for file in key_files:
                file_path = os.path.join(pkg_path, file)
                if os.path.exists(file_path):
                    self.log_success(f"文件存在: {file}")
                else:
                    self.log_error(f"文件缺失: {file}")
                    
        else:
            self.log_error("未找到pkg_gazebo包")
            return False
            
        return True
    
    def check_build_status(self):
        """检查构建状态"""
        print("\n🔍 检查构建状态...")
        
        # 检查install目录
        if os.path.exists('install'):
            self.log_success("install目录存在")
            
            # 检查pkg_gazebo是否已构建
            pkg_install_path = 'install/pkg_gazebo'
            if os.path.exists(pkg_install_path):
                self.log_success("pkg_gazebo已构建")
            else:
                self.log_warning("pkg_gazebo未构建，需要运行colcon build")
                
        else:
            self.log_warning("install目录不存在，需要构建工作空间")
            
        return True
    
    def check_urdf_syntax(self):
        """检查URDF语法"""
        print("\n🔍 检查URDF语法...")
        
        urdf_files = [
            'src/pkg_gazebo/urdf/urbubing.urdf.xacro'
        ]
        
        for urdf_file in urdf_files:
            if os.path.exists(urdf_file):
                # 尝试处理xacro文件
                success, output = self.run_command(f"xacro {urdf_file}")
                if success:
                    self.log_success(f"URDF语法正确: {urdf_file}")
                else:
                    self.log_error(f"URDF语法错误: {urdf_file}")
                    self.log_info(f"错误详情: {output}")
            else:
                self.log_error(f"URDF文件不存在: {urdf_file}")
                
        return True
    
    def print_summary(self):
        """打印诊断总结"""
        print("\n" + "="*60)
        print("🏁 系统诊断总结")
        print("="*60)
        
        print(f"✅ 成功项目: {len([msg for msg in self.info if '成功' in str(msg)])}")
        print(f"⚠️  警告项目: {len(self.warnings)}")
        print(f"❌ 错误项目: {len(self.errors)}")
        
        if self.errors:
            print("\n❌ 需要解决的错误:")
            for error in self.errors:
                print(f"  - {error}")
                
        if self.warnings:
            print("\n⚠️  需要注意的警告:")
            for warning in self.warnings:
                print(f"  - {warning}")
                
        print("\n" + "="*60)
        
        if not self.errors:
            print("🎉 系统检查通过！可以开始使用机器人仿真系统。")
        else:
            print("🔧 请先解决上述错误，然后重新运行诊断。")
    
    def run_full_diagnostics(self):
        """运行完整诊断"""
        print("🚀 开始系统诊断...")
        print("这将检查ROS2环境、依赖包、工作空间和项目文件")
        
        self.check_ros2_installation()
        self.check_dependencies()
        self.check_workspace()
        self.check_build_status()
        self.check_urdf_syntax()
        
        self.print_summary()

def main():
    diagnostics = SystemDiagnostics()
    diagnostics.run_full_diagnostics()

if __name__ == '__main__':
    main()
