#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import JointState
from geometry_msgs.msg import Twist
from std_msgs.msg import Float64MultiArray
import time
import math

class PhysicsValidator(Node):
    def __init__(self):
        super().__init__('physics_validator')
        
        # 订阅关节状态
        self.joint_state_sub = self.create_subscription(
            JointState,
            '/joint_states',
            self.joint_state_callback,
            10
        )
        
        # 发布关节命令（用于测试关节限位）
        self.joint_cmd_pub = self.create_publisher(
            Float64MultiArray,
            '/gimbal_controller/commands',
            10
        )
        
        # 存储关节状态
        self.joint_states = {}
        self.test_phase = 0
        self.test_start_time = time.time()
        
        # 创建定时器进行测试
        self.test_timer = self.create_timer(2.0, self.run_physics_tests)
        
        self.get_logger().info('Physics Validator Started')
        self.get_logger().info('Will test: joint limits, stability, collision detection')
        
    def joint_state_callback(self, msg):
        # 更新关节状态
        for i, name in enumerate(msg.name):
            self.joint_states[name] = {
                'position': msg.position[i] if i < len(msg.position) else 0.0,
                'velocity': msg.velocity[i] if i < len(msg.velocity) else 0.0
            }
    
    def run_physics_tests(self):
        current_time = time.time()
        elapsed = current_time - self.test_start_time
        
        if self.test_phase == 0:
            self.test_joint_limits()
            self.test_phase = 1
            
        elif self.test_phase == 1 and elapsed > 10:
            self.test_stability()
            self.test_phase = 2
            
        elif self.test_phase == 2 and elapsed > 20:
            self.analyze_results()
            self.test_phase = 3
            
        elif self.test_phase == 3:
            self.get_logger().info('Physics validation complete. Check results above.')
            self.test_timer.cancel()
    
    def test_joint_limits(self):
        self.get_logger().info('=== Testing Joint Limits ===')
        
        # 测试pitch关节限位
        if 'pitch_Joint' in self.joint_states:
            pitch_pos = self.joint_states['pitch_Joint']['position']
            pitch_lower = -0.7854  # -45 degrees
            pitch_upper = 0.5236   # 30 degrees
            
            self.get_logger().info(f'Pitch joint position: {pitch_pos:.3f} rad ({math.degrees(pitch_pos):.1f}°)')
            self.get_logger().info(f'Pitch limits: [{pitch_lower:.3f}, {pitch_upper:.3f}] rad')
            
            if pitch_lower <= pitch_pos <= pitch_upper:
                self.get_logger().info('✅ Pitch joint within limits')
            else:
                self.get_logger().warn('❌ Pitch joint outside limits!')
        
        # 发送测试命令到pitch关节
        cmd_msg = Float64MultiArray()
        cmd_msg.data = [0.0, 0.4]  # yaw=0, pitch=0.4 rad (约23度)
        self.joint_cmd_pub.publish(cmd_msg)
        self.get_logger().info('Sent test command: yaw=0°, pitch=23°')
    
    def test_stability(self):
        self.get_logger().info('=== Testing Stability ===')
        
        # 检查机器人是否稳定（轮子是否接触地面）
        wheel_joints = ['wheel_lf_Joint', 'wheel_rf_Joint', 'wheel_rb_Joint', 'wheel_lb_Joint']
        stable = True
        
        for wheel in wheel_joints:
            if wheel in self.joint_states:
                vel = abs(self.joint_states[wheel]['velocity'])
                if vel > 0.1:  # 如果轮子在无外力情况下还在转动，可能不稳定
                    stable = False
                    self.get_logger().warn(f'❌ {wheel} unstable, velocity: {vel:.3f}')
        
        if stable:
            self.get_logger().info('✅ Robot appears stable')
        
        # 检查重心（通过base_link的稳定性推断）
        if 'yaw_Joint' in self.joint_states:
            yaw_vel = abs(self.joint_states['yaw_Joint']['velocity'])
            if yaw_vel < 0.01:
                self.get_logger().info('✅ Center of gravity appears balanced')
            else:
                self.get_logger().warn(f'❌ Possible center of gravity issue, yaw drift: {yaw_vel:.3f}')
    
    def analyze_results(self):
        self.get_logger().info('=== Physics Analysis Results ===')
        
        # 统计关节数量
        total_joints = len(self.joint_states)
        self.get_logger().info(f'Total joints detected: {total_joints}')
        
        # 检查所有关节是否正常
        expected_joints = [
            'wheel_lf_Joint', 'wheel_rf_Joint', 'wheel_rb_Joint', 'wheel_lb_Joint',
            'yaw_Joint', 'pitch_Joint', 'dials_Joint',
            'pitch_aid_Joint1', 'pitch_aid_Joint2', 'pitch_aid_Joint3'
        ]
        
        missing_joints = []
        for joint in expected_joints:
            if joint not in self.joint_states:
                missing_joints.append(joint)
        
        if missing_joints:
            self.get_logger().warn(f'❌ Missing joints: {missing_joints}')
        else:
            self.get_logger().info('✅ All expected joints present')
        
        # 显示当前所有关节状态
        self.get_logger().info('Current joint states:')
        for name, state in self.joint_states.items():
            pos_deg = math.degrees(state['position'])
            self.get_logger().info(f'  {name}: {state["position"]:.3f} rad ({pos_deg:.1f}°), vel: {state["velocity"]:.3f}')

def main(args=None):
    rclpy.init(args=args)
    
    validator = PhysicsValidator()
    
    try:
        rclpy.spin(validator)
    except KeyboardInterrupt:
        pass
    finally:
        validator.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
