#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import JointState
import time

class RobotTester(Node):
    def __init__(self):
        super().__init__('robot_tester')
        
        # 订阅关节状态
        self.joint_state_sub = self.create_subscription(
            JointState,
            '/joint_states',
            self.joint_state_callback,
            10
        )
        
        self.get_logger().info('Robot Tester Node Started')
        self.get_logger().info('Waiting for joint states...')
        
    def joint_state_callback(self, msg):
        self.get_logger().info(f'Received joint states for {len(msg.name)} joints:')
        for i, name in enumerate(msg.name):
            pos = msg.position[i] if i < len(msg.position) else 0.0
            vel = msg.velocity[i] if i < len(msg.velocity) else 0.0
            self.get_logger().info(f'  {name}: pos={pos:.3f}, vel={vel:.3f}')
        self.get_logger().info('---')

def main(args=None):
    rclpy.init(args=args)
    
    tester = RobotTester()
    
    try:
        rclpy.spin(tester)
    except KeyboardInterrupt:
        pass
    finally:
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
