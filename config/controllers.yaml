controller_manager:
  ros__parameters:
    update_rate: 100  # Hz

    # Wheel controllers
    wheel_controller:
      type: velocity_controllers/JointGroupVelocityController

    # Gimbal controllers
    gimbal_controller:
      type: position_controllers/JointGroupPositionController

    # Shooter controller
    shooter_controller:
      type: velocity_controllers/JointVelocityController

    # Joint state broadcaster
    joint_state_broadcaster:
      type: joint_state_broadcaster/JointStateBroadcaster

# Wheel controller configuration
wheel_controller:
  ros__parameters:
    joints:
      - wheel_lf_Joint
      - wheel_rf_Joint
      - wheel_rb_Joint
      - wheel_lb_Joint
    interface_name: velocity

# Gimbal controller configuration
gimbal_controller:
  ros__parameters:
    joints:
      - yaw_Joint
      - pitch_Joint
    interface_name: position

# Shooter controller configuration
shooter_controller:
  ros__parameters:
    joint: dials_Joint
    interface_name: velocity

# Joint state broadcaster configuration
joint_state_broadcaster:
  ros__parameters:
    joints:
      - wheel_lf_Joint
      - wheel_rf_Joint
      - wheel_rb_Joint
      - wheel_lb_Joint
      - yaw_Joint
      - pitch_Joint
      - pitch_aid_Joint1
      - pitch_aid_Joint2
      - pitch_aid_Joint3
      - dials_Joint
    interfaces:
      - position
      - velocity
