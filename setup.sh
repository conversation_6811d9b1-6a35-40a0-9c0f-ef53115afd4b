#!/bin/bash

# 🚗 高级麦克纳姆轮机器人键盘控制系统 - 安装脚本
# 作者: AI Assistant
# 版本: 1.0

set -e  # 遇到错误时退出

echo "🚗 开始安装高级麦克纳姆轮机器人键盘控制系统..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查ROS2环境
check_ros2() {
    print_info "检查ROS2环境..."
    if [ -z "$ROS_DISTRO" ]; then
        print_error "未检测到ROS2环境！请先安装并配置ROS2。"
        print_info "参考: https://docs.ros.org/en/humble/Installation.html"
        exit 1
    else
        print_success "检测到ROS2 $ROS_DISTRO"
    fi
}

# 检查必要的包
check_dependencies() {
    print_info "检查依赖包..."
    
    local packages=(
        "ros-$ROS_DISTRO-ros-gz-sim"
        "ros-$ROS_DISTRO-ros2-control"
        "ros-$ROS_DISTRO-ros2-controllers"
        "ros-$ROS_DISTRO-gz-ros2-control"
        "ros-$ROS_DISTRO-robot-state-publisher"
        "ros-$ROS_DISTRO-joint-state-publisher"
        "ros-$ROS_DISTRO-xacro"
    )
    
    local missing_packages=()
    
    for package in "${packages[@]}"; do
        if ! dpkg -l | grep -q "^ii  $package "; then
            missing_packages+=("$package")
        fi
    done
    
    if [ ${#missing_packages[@]} -ne 0 ]; then
        print_warning "发现缺失的依赖包，正在安装..."
        sudo apt update
        sudo apt install -y "${missing_packages[@]}"
        print_success "依赖包安装完成"
    else
        print_success "所有依赖包已安装"
    fi
}

# 编译项目
build_project() {
    print_info "编译项目..."
    
    # 检查是否在ROS2工作空间中
    if [ ! -f "package.xml" ]; then
        print_error "请在pkg_gazebo包目录中运行此脚本"
        exit 1
    fi
    
    # 返回到工作空间根目录
    cd ../../../
    
    # 编译项目
    print_info "正在编译 pkg_gazebo 包..."
    colcon build --packages-select pkg_gazebo --cmake-args -DCMAKE_BUILD_TYPE=Release
    
    if [ $? -eq 0 ]; then
        print_success "项目编译成功"
    else
        print_error "项目编译失败"
        exit 1
    fi
}

# 设置权限
set_permissions() {
    print_info "设置文件权限..."
    
    # 确保Python脚本可执行
    chmod +x install/pkg_gazebo/lib/pkg_gazebo/keyboard_teleop.py
    
    print_success "权限设置完成"
}

# 创建桌面快捷方式
create_desktop_shortcut() {
    print_info "创建桌面快捷方式..."
    
    local desktop_file="$HOME/Desktop/robot_keyboard_control.desktop"
    local workspace_path=$(pwd)
    
    cat > "$desktop_file" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=机器人键盘控制
Comment=启动高级麦克纳姆轮机器人键盘控制系统
Exec=gnome-terminal -- bash -c "cd $workspace_path && source install/setup.bash && ros2 launch pkg_gazebo keyboard_control.launch.py; exec bash"
Icon=applications-games
Terminal=false
Categories=Application;Game;
EOF
    
    chmod +x "$desktop_file"
    print_success "桌面快捷方式已创建: $desktop_file"
}

# 创建启动脚本
create_launch_script() {
    print_info "创建启动脚本..."
    
    local script_path="$HOME/start_robot_control.sh"
    local workspace_path=$(pwd)
    
    cat > "$script_path" << EOF
#!/bin/bash
# 高级麦克纳姆轮机器人键盘控制系统启动脚本

cd $workspace_path
source install/setup.bash

echo "🚗 启动高级麦克纳姆轮机器人键盘控制系统..."
echo "📖 使用说明请参考 README.md"
echo "🎮 快速参考请查看 QUICK_REFERENCE.md"
echo ""

ros2 launch pkg_gazebo keyboard_control.launch.py
EOF
    
    chmod +x "$script_path"
    print_success "启动脚本已创建: $script_path"
}

# 显示使用说明
show_usage() {
    echo ""
    echo "🎉 安装完成！"
    echo ""
    echo "📚 使用方法："
    echo "1. 完整启动（推荐）："
    echo "   $HOME/start_robot_control.sh"
    echo ""
    echo "2. 手动启动："
    echo "   cd $(pwd)"
    echo "   source install/setup.bash"
    echo "   ros2 launch pkg_gazebo keyboard_control.launch.py"
    echo ""
    echo "3. 使用桌面快捷方式（如果支持）"
    echo ""
    echo "📖 详细文档："
    echo "   - README.md: 完整使用说明"
    echo "   - QUICK_REFERENCE.md: 快速参考"
    echo ""
    echo "🎮 控制说明："
    echo "   - TAB: 切换控制模式（车辆 ↔ 云台）"
    echo "   - WASD: 车辆移动"
    echo "   - 箭头键: 云台控制"
    echo "   - 空格: 紧急停止"
    echo "   - h: 显示帮助"
    echo ""
    print_success "安装完成，享受您的机器人控制体验！"
}

# 主函数
main() {
    echo "=================================="
    echo "🚗 机器人键盘控制系统安装程序"
    echo "=================================="
    
    check_ros2
    check_dependencies
    build_project
    set_permissions
    create_desktop_shortcut
    create_launch_script
    show_usage
}

# 运行主函数
main "$@"
