{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/dev_ws/install/learning_interface/include/**", "/opt/ros/jazzy/include/**", "/home/<USER>/ros2_ws/src/azure_sky_ros2/src/controlko_controllers/include/**", "/home/<USER>/ros2_ws/src/azure_sky_ros2/src/controlko_hardware_interface/include/**", "/home/<USER>/ros2_ws/src/azure_sky_ros2/src/my_hardware_interface/include/**", "/home/<USER>/ros2_ws/src/azure_sky_ros2/src/ros2_socketcan/ros2_socketcan/include/**", "/home/<USER>/ros2_ws/src/azure_sky_ros2/src/serial/include/**", "/home/<USER>/ros2_ws/src/pkg_gazebo/include/**", "/home/<USER>/ros2_ws/src/rplidar_ros/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}