{"backtraceGraph": {"commands": ["install", "ament_index_register_resource", "ament_cmake_environment_generate_package_run_dependencies_marker", "include", "ament_execute_extensions", "ament_package", "ament_cmake_environment_generate_parent_prefix_path_marker", "ament_environment_hooks", "ament_generate_package_environment", "ament_index_register_package", "_ament_package"], "files": ["CMakeLists.txt", "/opt/ros/jazzy/share/ament_cmake_core/cmake/index/ament_index_register_resource.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment/ament_cmake_environment_package_hook.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_package.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/ament_environment_hooks.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/ament_cmake_environment_hooks_package_hook.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/ament_generate_package_environment.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/index/ament_index_register_package.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/index/ament_cmake_index_package_hook.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 27, "parent": 0}, {"command": 0, "file": 0, "line": 32, "parent": 0}, {"command": 0, "file": 0, "line": 37, "parent": 0}, {"command": 0, "file": 0, "line": 42, "parent": 0}, {"command": 5, "file": 0, "line": 46, "parent": 0}, {"command": 4, "file": 4, "line": 66, "parent": 5}, {"command": 3, "file": 3, "line": 48, "parent": 6}, {"file": 2, "parent": 7}, {"command": 2, "file": 2, "line": 47, "parent": 8}, {"command": 1, "file": 2, "line": 29, "parent": 9}, {"command": 0, "file": 1, "line": 105, "parent": 10}, {"command": 6, "file": 2, "line": 48, "parent": 8}, {"command": 1, "file": 2, "line": 43, "parent": 12}, {"command": 0, "file": 1, "line": 105, "parent": 13}, {"command": 3, "file": 3, "line": 48, "parent": 6}, {"file": 6, "parent": 15}, {"command": 7, "file": 6, "line": 20, "parent": 16}, {"command": 0, "file": 5, "line": 70, "parent": 17}, {"command": 0, "file": 5, "line": 87, "parent": 17}, {"command": 0, "file": 5, "line": 70, "parent": 17}, {"command": 0, "file": 5, "line": 87, "parent": 17}, {"command": 8, "file": 6, "line": 26, "parent": 16}, {"command": 0, "file": 7, "line": 91, "parent": 22}, {"command": 0, "file": 7, "line": 91, "parent": 22}, {"command": 0, "file": 7, "line": 91, "parent": 22}, {"command": 0, "file": 7, "line": 107, "parent": 22}, {"command": 0, "file": 7, "line": 120, "parent": 22}, {"command": 3, "file": 3, "line": 48, "parent": 6}, {"file": 9, "parent": 28}, {"command": 9, "file": 9, "line": 16, "parent": 29}, {"command": 1, "file": 8, "line": 29, "parent": 30}, {"command": 0, "file": 1, "line": 105, "parent": 31}, {"command": 10, "file": 4, "line": 68, "parent": 5}, {"command": 0, "file": 4, "line": 150, "parent": 33}, {"command": 0, "file": 4, "line": 157, "parent": 33}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "share/pkg_gazebo/", "paths": ["launch"], "type": "directory"}, {"backtrace": 2, "component": "Unspecified", "destination": "share/pkg_gazebo/", "paths": ["urdf"], "type": "directory"}, {"backtrace": 3, "component": "Unspecified", "destination": "share/pkg_gazebo/", "paths": ["meshes"], "type": "directory"}, {"backtrace": 4, "component": "Unspecified", "destination": "share/pkg_gazebo/", "paths": ["config"], "type": "directory"}, {"backtrace": 11, "component": "Unspecified", "destination": "share/ament_index/resource_index/package_run_dependencies", "paths": ["build/pkg_gazebo/ament_cmake_index/share/ament_index/resource_index/package_run_dependencies/pkg_gazebo"], "type": "file"}, {"backtrace": 14, "component": "Unspecified", "destination": "share/ament_index/resource_index/parent_prefix_path", "paths": ["build/pkg_gazebo/ament_cmake_index/share/ament_index/resource_index/parent_prefix_path/pkg_gazebo"], "type": "file"}, {"backtrace": 18, "component": "Unspecified", "destination": "share/pkg_gazebo/environment", "paths": ["/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/environment/ament_prefix_path.sh"], "type": "file"}, {"backtrace": 19, "component": "Unspecified", "destination": "share/pkg_gazebo/environment", "paths": ["build/pkg_gazebo/ament_cmake_environment_hooks/ament_prefix_path.dsv"], "type": "file"}, {"backtrace": 20, "component": "Unspecified", "destination": "share/pkg_gazebo/environment", "paths": ["/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/environment/path.sh"], "type": "file"}, {"backtrace": 21, "component": "Unspecified", "destination": "share/pkg_gazebo/environment", "paths": ["build/pkg_gazebo/ament_cmake_environment_hooks/path.dsv"], "type": "file"}, {"backtrace": 23, "component": "Unspecified", "destination": "share/pkg_gazebo", "paths": ["build/pkg_gazebo/ament_cmake_environment_hooks/local_setup.bash"], "type": "file"}, {"backtrace": 24, "component": "Unspecified", "destination": "share/pkg_gazebo", "paths": ["build/pkg_gazebo/ament_cmake_environment_hooks/local_setup.sh"], "type": "file"}, {"backtrace": 25, "component": "Unspecified", "destination": "share/pkg_gazebo", "paths": ["build/pkg_gazebo/ament_cmake_environment_hooks/local_setup.zsh"], "type": "file"}, {"backtrace": 26, "component": "Unspecified", "destination": "share/pkg_gazebo", "paths": ["build/pkg_gazebo/ament_cmake_environment_hooks/local_setup.dsv"], "type": "file"}, {"backtrace": 27, "component": "Unspecified", "destination": "share/pkg_gazebo", "paths": ["build/pkg_gazebo/ament_cmake_environment_hooks/package.dsv"], "type": "file"}, {"backtrace": 32, "component": "Unspecified", "destination": "share/ament_index/resource_index/packages", "paths": ["build/pkg_gazebo/ament_cmake_index/share/ament_index/resource_index/packages/pkg_gazebo"], "type": "file"}, {"backtrace": 34, "component": "Unspecified", "destination": "share/pkg_gazebo/cmake", "paths": ["build/pkg_gazebo/ament_cmake_core/pkg_gazeboConfig.cmake", "build/pkg_gazebo/ament_cmake_core/pkg_gazeboConfig-version.cmake"], "type": "file"}, {"backtrace": 35, "component": "Unspecified", "destination": "share/pkg_gazebo", "paths": ["package.xml"], "type": "file"}], "paths": {"build": ".", "source": "."}}