{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-ab3336ae84ac8ef132fb.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "pkg_gazebo", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "pkg_gazebo_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-pkg_gazebo_uninstall-064527173228cf45cb35.json", "name": "pkg_gazebo_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-f7ea5489991c4f06e847.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ros2_ws/src/pkg_gazebo/build/pkg_gazebo", "source": "/home/<USER>/ros2_ws/src/pkg_gazebo"}, "version": {"major": 2, "minor": 6}}