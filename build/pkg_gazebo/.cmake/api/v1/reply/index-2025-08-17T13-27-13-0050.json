{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.28"}, "version": {"isDirty": false, "major": 3, "minor": 28, "patch": 3, "string": "3.28.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-c97e2c4ad1ffc621090f.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}], "reply": {"client-colcon-cmake": {"codemodel-v2": {"jsonFile": "codemodel-v2-c97e2c4ad1ffc621090f.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}}}}