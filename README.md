# 🚗 高级麦克纳姆轮机器人键盘控制系统

这是一个基于ROS2和Gazebo Harmonic的高级键盘控制系统，用于控制配备麦克纳姆轮和云台炮台的机器人。

## 📋 系统概述

### 🤖 机器人特性
- **底盘系统**: 4个麦克纳姆轮，支持全向移动（前后、左右、斜向、旋转）
- **云台系统**: 偏航(yaw)和俯仰(pitch)关节，360°旋转和上下调节
- **发射器系统**: 可控制的旋转发射装置
- **实时控制**: 双模式键盘控制（车辆模式 + 云台模式）

### 🛠️ 技术架构
- **ROS2 Humble/Iron**: 机器人操作系统
- **Gazebo Harmonic**: 物理仿真环境
- **ros2_control**: 硬件抽象和控制框架
- **Python**: 键盘控制节点实现

## 🚀 快速开始

### 前置条件
确保已安装以下软件：
```bash
# ROS2 Humble/Iron
# Gazebo Harmonic
# 相关ROS2包
sudo apt install ros-humble-ros-gz-sim ros-humble-ros2-control ros-humble-ros2-controllers
```

### 🎮 方法一：完整启动（推荐）
启动完整的仿真环境和键盘控制：
```bash
# 编译项目
cd ~/ros2_ws
colcon build --packages-select pkg_gazebo

# 加载环境
source install/setup.bash

# 启动完整系统（Gazebo + 机器人 + 控制器 + 键盘控制）
ros2 launch pkg_gazebo keyboard_control.launch.py
```

### 🖥️ 方法二：分步启动
如果需要更精细的控制：

**步骤1: 启动Gazebo仿真**
```bash
ros2 launch pkg_gazebo gazebo.launch.py
```

**步骤2: 启动键盘控制（新终端）**
```bash
ros2 launch pkg_gazebo simple_keyboard_control.launch.py
```

## 🎯 控制说明

### 🔄 模式切换
系统有两种控制模式：
- **🚗 车辆控制模式**: 控制机器人底盘移动
- **🎯 云台控制模式**: 控制云台和发射器

**切换方式**: 按 `TAB` 键在两种模式间切换

### 🚗 车辆控制模式

#### 基本移动
| 按键 | 功能 | 说明 |
|------|------|------|
| `w` | 前进 | 向前移动 |
| `s` | 后退 | 向后移动 |
| `a` | 左移 | 向左平移 |
| `d` | 右移 | 向右平移 |
| `q` | 左转 | 逆时针旋转 |
| `e` | 右转 | 顺时针旋转 |

#### 斜向移动（麦克纳姆轮特色）
| 按键 | 功能 | 说明 |
|------|------|------|
| `r` | 右前斜移 | 向右前方45°移动 |
| `t` | 左前斜移 | 向左前方45°移动 |
| `f` | 右后斜移 | 向右后方45°移动 |
| `g` | 左后斜移 | 向左后方45°移动 |

### 🎯 云台控制模式

#### 云台调节
| 按键 | 功能 | 说明 |
|------|------|------|
| `↑` | 俯仰向上 | 炮管向上调节 |
| `↓` | 俯仰向下 | 炮管向下调节 |
| `←` | 偏航向左 | 云台向左旋转 |
| `→` | 偏航向右 | 云台向右旋转 |

#### 发射器控制
| 按键 | 功能 | 说明 |
|------|------|------|
| `z` | 启动发射器 | 以最大速度启动 |
| `x` | 停止发射器 | 完全停止发射器 |
| `c` | 发射器加速 | 增加发射器转速 |
| `v` | 发射器减速 | 降低发射器转速 |

### ⚙️ 速度调节（通用）
| 按键 | 功能 | 说明 |
|------|------|------|
| `u` | 增加线速度 | 提高前进/后退/平移速度 |
| `j` | 减少线速度 | 降低前进/后退/平移速度 |
| `i` | 增加角速度 | 提高旋转速度 |
| `k` | 减少角速度 | 降低旋转速度 |
| `o` | 增加云台速度 | 提高云台调节速度 |
| `l` | 减少云台速度 | 降低云台调节速度 |

### 🛑 紧急控制
| 按键 | 功能 | 说明 |
|------|------|------|
| `空格` | 紧急停止 | 停止所有运动 |
| `h` | 显示帮助 | 显示控制说明 |
| `Ctrl+C` | 退出程序 | 安全退出控制程序 |

## 📊 实时状态显示

控制程序会实时显示：
- 当前控制模式（车辆/云台）
- 车辆运动状态（线速度、角速度）
- 云台位置（偏航角、俯仰角）
- 发射器状态（转速）
- 当前速度设置

## 🔧 高级配置

### 参数调节
可以在 `src/keyboard_teleop.py` 中调节以下参数：

```python
# 车辆运动参数
self.linear_speed = 1.0      # 默认线速度 (m/s)
self.angular_speed = 1.0     # 默认角速度 (rad/s)
self.speed_step = 0.1        # 速度调节步长

# 云台参数
self.gimbal_speed = 0.5      # 云台调节速度 (rad/s)
self.gimbal_step = 0.1       # 云台调节步长 (rad)

# 发射器参数
self.shooter_max_speed = 10.0  # 最大发射器速度
```

### 控制器配置
控制器参数在 `config/controllers.yaml` 中配置：
- 轮子控制器：速度控制模式
- 云台控制器：位置控制模式
- 发射器控制器：速度控制模式

## 🐛 故障排除

### 常见问题

**1. 键盘控制无响应**
```bash
# 检查控制器状态
ros2 control list_controllers

# 重新启动控制器
ros2 control load_controller wheel_controller
ros2 control load_controller gimbal_controller
ros2 control load_controller shooter_controller
```

**2. 机器人不移动**
```bash
# 检查话题连接
ros2 topic list | grep cmd_vel
ros2 topic echo /wheel_controller/commands

# 检查机器人是否正确生成
ros2 service call /gazebo/get_model_list gazebo_msgs/srv/GetModelList
```

**3. 云台控制异常**
```bash
# 检查云台控制器
ros2 topic echo /gimbal_controller/commands

# 检查关节状态
ros2 topic echo /joint_states
```

**4. 终端显示异常**
- 确保在支持ANSI转义序列的终端中运行
- 推荐使用 gnome-terminal 或 xterm

### 调试命令
```bash
# 查看所有话题
ros2 topic list

# 监控控制命令
ros2 topic echo /wheel_controller/commands
ros2 topic echo /gimbal_controller/commands
ros2 topic echo /shooter_controller/command

# 查看机器人状态
ros2 topic echo /joint_states

# 检查节点状态
ros2 node list
ros2 node info /advanced_keyboard_teleop
```

## 📁 项目结构

```
pkg_gazebo/
├── launch/
│   ├── gazebo.launch.py              # Gazebo仿真启动
│   ├── keyboard_control.launch.py   # 完整系统启动
│   └── simple_keyboard_control.launch.py  # 简单键盘控制
├── src/
│   └── keyboard_teleop.py           # 高级键盘控制节点
├── urdf/
│   └── urbubing.urdf.xacro          # 机器人模型定义
├── config/
│   └── controllers.yaml             # 控制器配置
├── worlds/
│   └── empty.world                  # Gazebo世界文件
├── meshes/                          # 3D网格文件
└── README.md                        # 本文档
```

## 🎯 使用技巧

1. **平滑控制**: 使用速度调节功能找到最适合的控制速度
2. **组合操作**: 可以同时按住多个移动键实现复杂运动
3. **模式切换**: 频繁使用TAB键在车辆和云台模式间切换
4. **紧急停止**: 遇到异常情况立即按空格键停止所有运动
5. **状态监控**: 注意观察实时状态显示，了解当前系统状态

## 📞 技术支持

如遇到问题，请检查：
1. ROS2环境是否正确配置
2. Gazebo Harmonic是否正常运行
3. 所有依赖包是否已安装
4. 控制器是否正常加载

---

🎮 **享受您的机器人控制体验！**
